/**
 * Main layout component with resizable panes
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Allotment } from 'allotment';
import { useApp } from '@/contexts/AppContext';
import { useResponsive } from '@/hooks/useResponsive';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { EditorPane } from '../Editor/EditorPane';
import { PreviewPane } from '../Preview/PreviewPane';
import { StatusBar } from './StatusBar';
import 'allotment/dist/style.css';

interface MainLayoutProps {
  children?: React.ReactNode;
}

export function MainLayout({ children: _children }: MainLayoutProps) {
  const { state } = useApp();
  const responsive = useResponsive();
  const [sidebarWidth, setSidebarWidth] = useState(300);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(responsive.isMobile);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [mobileView, setMobileView] = useState<'edit' | 'preview'>('edit');
  const [_editorScrollPosition, setEditorScrollPosition] = useState<{
    scrollTop: number;
    scrollHeight: number;
    clientHeight: number;
  } | null>(null);
  const [previewScrollPosition, setPreviewScrollPosition] = useState<{
    scrollTop: number;
    scrollHeight: number;
    clientHeight: number;
  } | null>(null);

  // Handle responsive behavior
  useEffect(() => {
    if (responsive.isMobile) {
      setIsSidebarCollapsed(true);
      setShowMobileMenu(false);
    } else if (responsive.isTablet) {
      // On tablet, keep sidebar collapsed by default but allow toggle
      const savedCollapsed = localStorage.getItem('mdeditor_sidebar_collapsed');
      if (savedCollapsed === null) {
        setIsSidebarCollapsed(true);
      }
    }
  }, [responsive.deviceType]);

  // Initialize layout
  useEffect(() => {
    // Load saved layout preferences
    const savedSidebarWidth = localStorage.getItem('mdeditor_sidebar_width');
    const savedSidebarCollapsed = localStorage.getItem('mdeditor_sidebar_collapsed');

    if (savedSidebarWidth && !responsive.isMobile) {
      setSidebarWidth(parseInt(savedSidebarWidth, 10));
    }

    if (savedSidebarCollapsed && !responsive.isMobile) {
      setIsSidebarCollapsed(savedSidebarCollapsed === 'true');
    }

    setIsLoading(false);
  }, [responsive.isMobile]);

  // Save layout preferences
  useEffect(() => {
    if (!isLoading) {
      localStorage.setItem('mdeditor_sidebar_width', sidebarWidth.toString());
      localStorage.setItem('mdeditor_sidebar_collapsed', isSidebarCollapsed.toString());
    }
  }, [sidebarWidth, isSidebarCollapsed, isLoading]);

  const handleSidebarToggle = () => {
    if (responsive.isMobile) {
      setShowMobileMenu(!showMobileMenu);
    } else {
      setIsSidebarCollapsed(!isSidebarCollapsed);
    }
  };

  // Handle scroll synchronization with improved debouncing
  const handleEditorScroll = useCallback((scrollTop: number, scrollHeight: number, clientHeight: number) => {
    if (state.settings.previewMode === 'side') {
      // Use requestAnimationFrame for smoother synchronization
      requestAnimationFrame(() => {
        setPreviewScrollPosition({ scrollTop, scrollHeight, clientHeight });
      });
    }
  }, [state.settings.previewMode]);

  const handlePreviewScroll = useCallback((scrollTop: number, scrollHeight: number, clientHeight: number) => {
    if (state.settings.previewMode === 'side') {
      // Use requestAnimationFrame for smoother synchronization
      requestAnimationFrame(() => {
        setEditorScrollPosition({ scrollTop, scrollHeight, clientHeight });
      });
    }
  }, [state.settings.previewMode]);

  // Handle mobile view toggle - removed as not used
  // const toggleMobileView = () => {
  //   setMobileView(current => current === 'edit' ? 'preview' : 'edit');
  // };

  const renderMobileContent = () => (
    <div className="h-full flex flex-col">
      <div className="flex justify-center gap-1 p-2 border-b border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setMobileView('edit')}
          className={`px-4 py-2 rounded-lg transition-colors ${
            mobileView === 'edit'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400'
          }`}
        >
          Editor
        </button>
        <button
          onClick={() => setMobileView('preview')}
          className={`px-4 py-2 rounded-lg transition-colors ${
            mobileView === 'preview'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400'
          }`}
        >
          Preview
        </button>
      </div>
      <div className="flex-1 min-h-0 overflow-hidden relative">
        {mobileView === 'edit' ? (
          <EditorPane onScroll={handleEditorScroll} />
        ) : (
          <PreviewPane
            onScroll={handlePreviewScroll}
            scrollToPosition={previewScrollPosition}
          />
        )}
      </div>
    </div>
  );

  const renderTabletContent = () => {
    const { previewMode } = state.settings;
    return (
      <div className="h-full flex">
        <div className={`flex-1 min-w-0 ${previewMode === 'preview' ? 'hidden' : ''}`}>
          <EditorPane onScroll={handleEditorScroll} />
        </div>
        {previewMode !== 'edit' && (
          <div className="flex-1 min-w-0 border-l border-gray-200 dark:border-gray-700">
            <PreviewPane
              onScroll={handlePreviewScroll}
              scrollToPosition={previewScrollPosition}
            />
          </div>
        )}
      </div>
    );
  };

  const renderDesktopContent = () => {
    const { previewMode } = state.settings;
    return (
      <Allotment
        defaultSizes={[50, 50]}
        minSize={300}
        snap
        className="editor-preview-split"
        onDragEnd={() => {
          // Reset scroll positions after resize
          setEditorScrollPosition(null);
          setPreviewScrollPosition(null);
        }}
      >
        <Allotment.Pane visible={previewMode !== 'preview'} minSize={300}>
          <EditorPane onScroll={handleEditorScroll} />
        </Allotment.Pane>
        <Allotment.Pane visible={previewMode !== 'edit'} minSize={300}>
          <PreviewPane
            onScroll={handlePreviewScroll}
            scrollToPosition={previewScrollPosition}
          />
        </Allotment.Pane>
      </Allotment>
    );
  };

  const renderMainContent = () => {
    if (responsive.isMobile) {
      return renderMobileContent();
    }

    if (responsive.isTablet) {
      return renderTabletContent();
    }

    return renderDesktopContent();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="relative">
            <div className="w-16 h-16 mx-auto mb-4">
              <div className="absolute inset-0 rounded-full border-4 border-blue-200 dark:border-blue-800"></div>
              <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-600 animate-spin"></div>
              <div className="absolute inset-2 rounded-full border-2 border-transparent border-t-blue-400 animate-spin animation-delay-150"></div>
            </div>
          </div>
          <p className="text-gray-600 dark:text-gray-400 animate-pulse">Loading Markdown Editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col dark">
      {/* Header */}
      <Header onSidebarToggle={handleSidebarToggle} />

      {/* Main content area */}
      <div className="flex-1 flex overflow-hidden relative">
        {/* Mobile sidebar overlay */}
        {responsive.isMobile && showMobileMenu && (
          <>
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setShowMobileMenu(false)}
            />
            <div className="fixed left-0 top-16 bottom-0 w-80 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 z-50 transform transition-transform duration-300">
              <Sidebar />
            </div>
          </>
        )}

        {/* Desktop/Tablet sidebar */}
        {!responsive.isMobile && (
          <div
            className={`h-full bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out ${
              isSidebarCollapsed ? 'w-0 opacity-0' : `opacity-100`
            }`}
            style={{
              width: isSidebarCollapsed ? '0px' : `${responsive.isTablet ? Math.min(sidebarWidth, 250) : sidebarWidth}px`,
              minWidth: isSidebarCollapsed ? '0px' : '200px'
            }}
          >
            <div className={`h-full transition-opacity duration-200 ${isSidebarCollapsed ? 'opacity-0' : 'opacity-100'}`}>
              <Sidebar />
            </div>
          </div>
        )}

        {/* Resizer */}
        {!isSidebarCollapsed && (
          <div
            className="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-blue-500 cursor-col-resize transition-colors duration-200 flex-shrink-0"
            onMouseDown={(e) => {
              const startX = e.clientX;
              const startWidth = sidebarWidth;

              const handleMouseMove = (e: MouseEvent) => {
                const newWidth = Math.max(200, Math.min(600, startWidth + (e.clientX - startX)));
                setSidebarWidth(newWidth);
              };

              const handleMouseUp = () => {
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
              };

              document.addEventListener('mousemove', handleMouseMove);
              document.addEventListener('mouseup', handleMouseUp);
            }}
          />
        )}

        {/* Main editor/preview area */}
        <div className={`flex-1 h-full bg-white dark:bg-gray-900 transition-all duration-300 ease-in-out ${
          isSidebarCollapsed ? 'ml-0' : ''
        }`}>
          {renderMainContent()}
        </div>
      </div>

      {/* Status bar */}
      <StatusBar />

      {/* Custom styles */}
      <style jsx global>{`
        :root {
          --border-color: #e5e7eb;
          --sidebar-bg: #f9fafb;
          --sidebar-border: #e5e7eb;
        }

        .dark {
          --border-color: #374151;
          --sidebar-bg: #1f2937;
          --sidebar-border: #374151;
        }

        .allotment > .allotment-pane {
          overflow: hidden;
        }

        .allotment-separator {
          background-color: var(--border-color) !important;
          transition: background-color 0.2s ease;
        }

        .allotment-separator:hover {
          background-color: #3b82f6 !important;
        }

        /* Responsive design and split pane styling */
        .editor-preview-split {
          height: 100%;
          width: 100%;
        }

        /* Mobile view styles */
        @media (max-width: 768px) {
          .editor-preview-split {
            display: none;
          }

          .h-screen {
            height: 100vh;
            height: 100dvh;
          }

          button {
            min-height: 44px;
            min-width: 44px;
          }

          input, textarea {
            font-size: 16px;
          }
        }

        /* Tablet optimizations */
        @media (max-width: 1024px) and (min-width: 769px) {
          .editor-preview-split .allotment-separator {
            width: 3px;
          }

          .allotment-pane {
            min-width: 0 !important;
          }
        }

        /* Desktop split pane styling */
        @media (min-width: 1025px) {
          .editor-preview-split .allotment-separator {
            width: 4px;
            background-color: var(--border-color);
            transition: background-color 0.2s ease;
          }

          .editor-preview-split .allotment-separator:hover {
            background-color: #3b82f6;
            cursor: col-resize;
          }
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        ::-webkit-scrollbar-track {
          background: transparent;
        }

        ::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }

        .dark ::-webkit-scrollbar-thumb {
          background: #4b5563;
        }

        .dark ::-webkit-scrollbar-thumb:hover {
          background: #6b7280;
        }

        /* Focus styles */
        .allotment:focus-within .allotment-separator {
          background-color: #3b82f6 !important;
        }

        /* Animation for smooth transitions */
        .transition-layout {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Print styles */
        @media print {
          .no-print {
            display: none !important;
          }

          .print-full-width {
            width: 100% !important;
            max-width: none !important;
          }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
          .allotment-separator {
            background-color: #000 !important;
          }

          .dark .allotment-separator {
            background-color: #fff !important;
          }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          .transition-layout,
          .allotment-separator {
            transition: none !important;
          }
        }
      `}</style>
    </div>
  );
}
