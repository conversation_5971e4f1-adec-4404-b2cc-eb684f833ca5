/**
 * Main application context for state management
 */

'use client';

import React, { createContext, useContext, useReducer, useEffect, useCallback, ReactNode } from 'react';
import {
  AppState,
  EditorAction,
  FileManagerAction,
  PreviewAction,
  SettingsAction,
  EditorSettings,
  MarkdownFile
} from '@/types';
import { localStorage } from '@/utils/file';
import { markdownToHtml } from '@/utils/markdown';
import { useProductionSessionManagement } from '@/hooks/useSessionManagement';


// Initial state
const initialState: AppState = {
  editor: {
    currentFile: null,
    content: '',
    isModified: false,
    isLoading: false,
    error: null,
    cursorPosition: { line: 1, column: 1 }
  },
  preview: {
    htmlContent: '',
    isLoading: false,
    error: null,
    scrollPosition: 0
  },
  fileManager: {
    currentDirectory: '/',
    selectedFiles: [],
    files: [],
    isLoading: false,
    error: null
  },
  settings: {
    theme: 'dark',
    fontSize: 14,
    lineNumbers: true,
    wordWrap: true,
    previewMode: 'side',
    autoSave: true,
    autoSaveInterval: 30000 // 30 seconds
  },
  collaboration: {
    isEnabled: false,
    users: [],
    roomId: null,
    isConnected: false
  },
  cloudProviders: []
};

// Action types
type AppAction =
  | { type: 'EDITOR_ACTION'; payload: EditorAction }
  | { type: 'FILE_MANAGER_ACTION'; payload: FileManagerAction }
  | { type: 'PREVIEW_ACTION'; payload: PreviewAction }
  | { type: 'SETTINGS_ACTION'; payload: SettingsAction }
  | { type: 'LOAD_INITIAL_DATA' }
  | { type: 'RESET_STATE' };

// Reducers
function editorReducer(state: AppState['editor'], action: EditorAction): AppState['editor'] {
  switch (action.type) {
    case 'SET_CONTENT':
      return { ...state, content: action.payload, isModified: true };
    case 'SET_CURRENT_FILE':
      return {
        ...state,
        currentFile: action.payload,
        content: action.payload?.content || '',
        isModified: false
      };
    case 'SET_MODIFIED':
      return { ...state, isModified: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_CURSOR_POSITION':
      return { ...state, cursorPosition: action.payload };
    default:
      return state;
  }
}

function fileManagerReducer(
  state: AppState['fileManager'],
  action: FileManagerAction
): AppState['fileManager'] {
  switch (action.type) {
    case 'SET_FILES':
      return { ...state, files: action.payload };
    case 'ADD_FILE':
      return { ...state, files: [...state.files, action.payload] };
    case 'UPDATE_FILE':
      return {
        ...state,
        files: state.files.map(file =>
          file.id === action.payload.id ? action.payload : file
        )
      };
    case 'DELETE_FILE':
      return {
        ...state,
        files: state.files.filter(file => file.id !== action.payload)
      };
    case 'SET_CURRENT_DIRECTORY':
      return { ...state, currentDirectory: action.payload };
    case 'SET_SELECTED_FILES':
      return { ...state, selectedFiles: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    default:
      return state;
  }
}

function previewReducer(state: AppState['preview'], action: PreviewAction): AppState['preview'] {
  switch (action.type) {
    case 'SET_HTML_CONTENT':
      return { ...state, htmlContent: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_SCROLL_POSITION':
      return { ...state, scrollPosition: action.payload };
    default:
      return state;
  }
}

function settingsReducer(state: AppState['settings'], action: SettingsAction): AppState['settings'] {
  switch (action.type) {
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    case 'SET_FONT_SIZE':
      return { ...state, fontSize: action.payload };
    case 'SET_LINE_NUMBERS':
      return { ...state, lineNumbers: action.payload };
    case 'SET_WORD_WRAP':
      return { ...state, wordWrap: action.payload };
    case 'SET_PREVIEW_MODE':
      return { ...state, previewMode: action.payload };
    case 'SET_AUTO_SAVE':
      return { ...state, autoSave: action.payload };
    case 'SET_AUTO_SAVE_INTERVAL':
      return { ...state, autoSaveInterval: action.payload };
    default:
      return state;
  }
}

// Main reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'EDITOR_ACTION':
      return { ...state, editor: editorReducer(state.editor, action.payload) };
    case 'FILE_MANAGER_ACTION':
      return { ...state, fileManager: fileManagerReducer(state.fileManager, action.payload) };
    case 'PREVIEW_ACTION':
      return { ...state, preview: previewReducer(state.preview, action.payload) };
    case 'SETTINGS_ACTION':
      const newSettings = settingsReducer(state.settings, action.payload);
      // Save settings to localStorage
      localStorage.saveSettings(newSettings);
      return { ...state, settings: newSettings };
    case 'LOAD_INITIAL_DATA':
      const savedFiles = localStorage.loadFiles();
      const savedSettings = localStorage.loadSettings();
      return {
        ...state,
        fileManager: { ...state.fileManager, files: savedFiles },
        settings: savedSettings ? { ...state.settings, ...savedSettings } : state.settings
      };
    case 'RESET_STATE':
      localStorage.clear();
      return initialState;
    default:
      return state;
  }
}

// Context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;

  // Helper functions
  updateContent: (content: string) => void;
  openFile: (file: MarkdownFile) => void;
  saveCurrentFile: () => void;
  createNewFile: (name: string, content?: string) => void;
  deleteFile: (fileId: string) => void;
  updatePreview: (content: string) => void;
  updateSettings: (settings: Partial<EditorSettings>) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider component
interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Initialize session management
  const sessionManager = useProductionSessionManagement();

  // Load initial data on mount
  useEffect(() => {
    dispatch({ type: 'LOAD_INITIAL_DATA' });

    // Check for session backup
    if (typeof window !== 'undefined') {
      const backup = window.localStorage.getItem('mdeditor-session-backup');
      if (backup) {
        try {
          const backupData = JSON.parse(backup);
          if (backupData.content && Date.now() - backupData.timestamp < 86400000) { // 24 hours
            // Restore from backup
            dispatch({
              type: 'EDITOR_ACTION',
              payload: { type: 'SET_CONTENT', payload: backupData.content }
            });

            // Clear the backup
            window.localStorage.removeItem('mdeditor-session-backup');
          }
        } catch (error) {
          console.warn('Failed to restore session backup:', error);
        }
      }
    }
  }, []);

  // Handle theme changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const root = document.documentElement;
      const body = document.body;

      if (state.settings.theme === 'dark') {
        root.classList.add('dark');
        body.classList.add('dark');
      } else {
        root.classList.remove('dark');
        body.classList.remove('dark');
      }

      // Force a repaint
      root.style.colorScheme = state.settings.theme;
    }
  }, [state.settings.theme]);

  // Define updatePreview function first
  const updatePreview = useCallback((content: string) => {
    try {
      dispatch({
        type: 'PREVIEW_ACTION',
        payload: { type: 'SET_LOADING', payload: true }
      });

      // Ensure content is a string
      const contentStr = String(content || '');
      const htmlContent = markdownToHtml(contentStr);

      // Verify we got a string back
      if (typeof htmlContent !== 'string') {
        throw new Error('Markdown processing returned invalid content type');
      }

      dispatch({
        type: 'PREVIEW_ACTION',
        payload: { type: 'SET_HTML_CONTENT', payload: htmlContent }
      });

      dispatch({
        type: 'PREVIEW_ACTION',
        payload: { type: 'SET_ERROR', payload: null }
      });
    } catch (error) {
      console.error('Preview update error:', error);
      dispatch({
        type: 'PREVIEW_ACTION',
        payload: { type: 'SET_ERROR', payload: `Failed to render preview: ${error instanceof Error ? error.message : 'Unknown error'}` }
      });
    } finally {
      dispatch({
        type: 'PREVIEW_ACTION',
        payload: { type: 'SET_LOADING', payload: false }
      });
    }
  }, []);

  // Auto-save functionality
  useEffect(() => {
    if (!state.settings.autoSave || !state.editor.isModified || !state.editor.currentFile) {
      return;
    }

    const autoSaveTimer = setTimeout(() => {
      saveCurrentFile();
    }, state.settings.autoSaveInterval);

    return () => clearTimeout(autoSaveTimer);
  }, [state.editor.content, state.editor.isModified, state.settings.autoSave, state.settings.autoSaveInterval]);

  // Update preview when content changes
  useEffect(() => {
    if (state.editor.content) {
      updatePreview(state.editor.content);
    }
  }, [state.editor.content, updatePreview]);

  // Save current content to localStorage for session backup
  useEffect(() => {
    if (typeof window !== 'undefined' && state.editor.content) {
      window.localStorage.setItem('mdeditor-current-content', state.editor.content);
    }
  }, [state.editor.content]);

  // Helper functions
  const updateContent = (content: string) => {
    dispatch({
      type: 'EDITOR_ACTION',
      payload: { type: 'SET_CONTENT', payload: content }
    });
  };

  const openFile = (file: MarkdownFile) => {
    dispatch({
      type: 'EDITOR_ACTION',
      payload: { type: 'SET_CURRENT_FILE', payload: file }
    });
  };

  const saveCurrentFile = () => {
    if (!state.editor.currentFile) return;

    const updatedFile: MarkdownFile = {
      ...state.editor.currentFile,
      content: state.editor.content,
      lastModified: new Date(),
      size: new Blob([state.editor.content]).size
    };

    dispatch({
      type: 'FILE_MANAGER_ACTION',
      payload: { type: 'UPDATE_FILE', payload: updatedFile }
    });

    dispatch({
      type: 'EDITOR_ACTION',
      payload: { type: 'SET_MODIFIED', payload: false }
    });

    // Save to localStorage
    const updatedFiles = state.fileManager.files.map(file =>
      file.id === updatedFile.id ? updatedFile : file
    );
    localStorage.saveFiles(updatedFiles);
  };

  const createNewFile = (name: string, content: string = '') => {
    const newFile: MarkdownFile = {
      id: `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      name: name.endsWith('.md') ? name : `${name}.md`,
      content,
      path: name,
      lastModified: new Date(),
      size: new Blob([content]).size,
      isDirectory: false
    };

    dispatch({
      type: 'FILE_MANAGER_ACTION',
      payload: { type: 'ADD_FILE', payload: newFile }
    });

    // Save to localStorage
    localStorage.saveFiles([...state.fileManager.files, newFile]);

    // Open the new file
    openFile(newFile);
  };

  const deleteFile = (fileId: string) => {
    dispatch({
      type: 'FILE_MANAGER_ACTION',
      payload: { type: 'DELETE_FILE', payload: fileId }
    });

    // If the deleted file is currently open, close it
    if (state.editor.currentFile?.id === fileId) {
      dispatch({
        type: 'EDITOR_ACTION',
        payload: { type: 'SET_CURRENT_FILE', payload: null }
      });
    }

    // Save to localStorage
    const updatedFiles = state.fileManager.files.filter(file => file.id !== fileId);
    localStorage.saveFiles(updatedFiles);
  };

  const updateSettings = (settings: Partial<EditorSettings>) => {
    Object.entries(settings).forEach(([key, value]) => {
      switch (key) {
        case 'theme':
          dispatch({
            type: 'SETTINGS_ACTION',
            payload: { type: 'SET_THEME', payload: value as 'light' | 'dark' }
          });
          break;
        case 'fontSize':
          dispatch({
            type: 'SETTINGS_ACTION',
            payload: { type: 'SET_FONT_SIZE', payload: value as number }
          });
          break;
        case 'lineNumbers':
          dispatch({
            type: 'SETTINGS_ACTION',
            payload: { type: 'SET_LINE_NUMBERS', payload: value as boolean }
          });
          break;
        case 'wordWrap':
          dispatch({
            type: 'SETTINGS_ACTION',
            payload: { type: 'SET_WORD_WRAP', payload: value as boolean }
          });
          break;
        case 'previewMode':
          dispatch({
            type: 'SETTINGS_ACTION',
            payload: { type: 'SET_PREVIEW_MODE', payload: value as 'side' | 'preview' | 'edit' }
          });
          break;
        case 'autoSave':
          dispatch({
            type: 'SETTINGS_ACTION',
            payload: { type: 'SET_AUTO_SAVE', payload: value as boolean }
          });
          break;
        case 'autoSaveInterval':
          dispatch({
            type: 'SETTINGS_ACTION',
            payload: { type: 'SET_AUTO_SAVE_INTERVAL', payload: value as number }
          });
          break;
      }
    });
  };

  const contextValue: AppContextType = {
    state,
    dispatch,
    updateContent,
    openFile,
    saveCurrentFile,
    createNewFile,
    deleteFile,
    updatePreview,
    updateSettings
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}



// Hook to use the context
export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
