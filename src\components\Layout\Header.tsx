/**
 * Header component with toolbar and navigation
 */

'use client';

import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { useResponsive } from '@/hooks/useResponsive';
import { exportMarkdown } from '@/utils/export';
import { readFileFromInput } from '@/utils/file';
import { ExportOptions } from '@/types';

interface HeaderProps {
  onSidebarToggle: () => void;
}

export function Header({ onSidebarToggle }: HeaderProps) {
  const { state, saveCurrentFile, createNewFile, updateSettings } = useApp();
  const responsive = useResponsive();
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [showNewFileDialog, setShowNewFileDialog] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [isExporting, setIsExporting] = useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleNewFile = () => {
    setShowNewFileDialog(true);
  };

  const handleCreateFile = () => {
    if (newFileName.trim()) {
      createNewFile(newFileName.trim());
      setNewFileName('');
      setShowNewFileDialog(false);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    for (const file of Array.from(files)) {
      try {
        const content = await readFileFromInput(file);
        createNewFile(file.name, content);
      } catch (error) {
        console.error('Failed to upload file:', error);
        alert(`Failed to upload ${file.name}: ${error}`);
      }
    }

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSave = () => {
    if (state.editor.currentFile) {
      saveCurrentFile();
    }
  };

  const handleExport = async (format: 'pdf' | 'docx' | 'txt' | 'html') => {
    if (!state.editor.currentFile || !state.editor.content) {
      alert('No file to export');
      return;
    }

    setIsExporting(true);
    setShowExportMenu(false);

    try {
      const options: ExportOptions = {
        format,
        includeMetadata: true,
        pageSize: 'A4',
        margins: { top: 20, right: 20, bottom: 20, left: 20 }
      };

      await exportMarkdown(
        state.editor.content,
        state.editor.currentFile.name,
        options
      );
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const handleThemeToggle = () => {
    updateSettings({
      theme: state.settings.theme === 'light' ? 'dark' : 'light'
    });
  };

  const handlePreviewModeChange = (mode: 'side' | 'preview' | 'edit') => {
    updateSettings({ previewMode: mode });
  };

  return (
    <header className="h-14 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center px-4 no-print">
      <div className="flex items-center space-x-2 md:space-x-4">
        <button
          onClick={onSidebarToggle}
          className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          title={responsive.isMobile ? "Toggle menu" : "Toggle sidebar"}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-md flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          {!responsive.isMobile && (
            <span className="font-semibold text-gray-900 dark:text-white">Markdown Editor</span>
          )}
        </div>
      </div>

      {/* Center section - File actions */}
      {responsive.isMobile ? (
        // Mobile: Show only essential buttons
        <div className="flex-1 flex items-center justify-center space-x-1">
          <button
            onClick={handleNewFile}
            className="p-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors"
            title="New file"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>

          <button
            onClick={handleSave}
            disabled={!state.editor.isModified}
            className="p-2 rounded-md bg-green-600 text-white hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            title="Save file"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </button>
        </div>
      ) : (
        // Desktop/Tablet: Show full buttons
        <div className="flex-1 flex items-center justify-center space-x-2">
          <button
            onClick={handleNewFile}
            className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            title="New file (Ctrl+N)"
          >
            New
          </button>

          <button
            onClick={handleUploadClick}
            className="px-3 py-1.5 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
            title="Upload files"
          >
            Upload
          </button>

          <button
            onClick={handleSave}
            disabled={!state.editor.isModified}
            className="px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            title="Save file (Ctrl+S)"
          >
            Save
          </button>

        {/* Export dropdown */}
        <div className="relative">
          <button
            onClick={() => setShowExportMenu(!showExportMenu)}
            disabled={!state.editor.currentFile || isExporting}
            className="px-3 py-1.5 text-sm bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-1"
            title="Export file"
          >
            {isExporting ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            )}
            <span>Export</span>
          </button>

          {showExportMenu && (
            <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
              <div className="py-1">
                <button
                  onClick={() => handleExport('md')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Markdown (.md)
                </button>
                <button
                  onClick={() => handleExport('txt')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Plain Text (.txt)
                </button>
                <button
                  onClick={() => handleExport('html')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  HTML (.html)
                </button>
                <button
                  onClick={() => handleExport('pdf')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  PDF (.pdf)
                </button>
                <button
                  onClick={() => handleExport('docx')}
                  className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Word Document (.docx)
                </button>
              </div>
            </div>
          )}
        </div>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".md,.markdown,.mdown,.mkd,.mdx,.txt"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Right section */}
      <div className="flex items-center space-x-1 md:space-x-2">
        {/* Preview mode toggle */}
        {responsive.isMobile ? (
          // Mobile: Simple toggle between edit and preview
          <button
            onClick={() => handlePreviewModeChange(state.settings.previewMode === 'edit' ? 'preview' : 'edit')}
            className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title={state.settings.previewMode === 'edit' ? 'Show preview' : 'Show editor'}
          >
            {state.settings.previewMode === 'edit' ? (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            )}
          </button>
        ) : (
          // Desktop/Tablet: Full toggle
          <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-md p-1">
            <button
              onClick={() => handlePreviewModeChange('edit')}
              className={`px-2 py-1 text-xs rounded ${
                state.settings.previewMode === 'edit'
                  ? 'bg-white dark:bg-gray-600 shadow-sm'
                  : 'hover:bg-gray-200 dark:hover:bg-gray-600'
              } transition-colors`}
              title="Editor only"
            >
              Edit
            </button>
            {!responsive.isTablet && (
              <button
                onClick={() => handlePreviewModeChange('side')}
                className={`px-2 py-1 text-xs rounded ${
                  state.settings.previewMode === 'side'
                    ? 'bg-white dark:bg-gray-600 shadow-sm'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-600'
                } transition-colors`}
                title="Side by side"
              >
                Split
              </button>
            )}
            <button
              onClick={() => handlePreviewModeChange('preview')}
              className={`px-2 py-1 text-xs rounded ${
                state.settings.previewMode === 'preview'
                  ? 'bg-white dark:bg-gray-600 shadow-sm'
                  : 'hover:bg-gray-200 dark:hover:bg-gray-600'
              } transition-colors`}
              title="Preview only"
            >
              Preview
            </button>
          </div>
        )}

        {/* Theme toggle */}
        <button
          onClick={handleThemeToggle}
          className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          title="Toggle theme"
        >
          {state.settings.theme === 'light' ? (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          )}
        </button>

        {/* Current file indicator */}
        {state.editor.currentFile && !responsive.isMobile && (
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <span className="truncate max-w-32">{state.editor.currentFile.name}</span>
            {state.editor.isModified && (
              <div className="w-2 h-2 bg-orange-500 rounded-full" title="Unsaved changes"></div>
            )}
          </div>
        )}

        {/* Mobile: Show only modified indicator */}
        {state.editor.currentFile && responsive.isMobile && state.editor.isModified && (
          <div className="w-2 h-2 bg-orange-500 rounded-full" title="Unsaved changes"></div>
        )}
      </div>

      {/* New file dialog */}
      {showNewFileDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Create New File
            </h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                File Name
              </label>
              <input
                type="text"
                value={newFileName}
                onChange={(e) => setNewFileName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleCreateFile();
                  } else if (e.key === 'Escape') {
                    setShowNewFileDialog(false);
                    setNewFileName('');
                  }
                }}
                placeholder="my-document.md"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                autoFocus
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                .md extension will be added automatically if not provided
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowNewFileDialog(false);
                  setNewFileName('');
                }}
                className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateFile}
                disabled={!newFileName.trim()}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close menus */}
      {(showExportMenu || showNewFileDialog) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setShowExportMenu(false);
            setShowNewFileDialog(false);
          }}
        ></div>
      )}
    </header>
  );
}
