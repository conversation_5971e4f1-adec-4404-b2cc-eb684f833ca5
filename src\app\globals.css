@import "tailwindcss";

/* Custom CSS Variables */
:root {
  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', <PERSON><PERSON><PERSON>, 'Courier New', monospace;
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* Base styles */
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: var(--font-sans);
  background: var(--background);
  color: var(--foreground);
}

/* Typography */
.font-mono {
  font-family: var(--font-mono);
}

/* Dark mode support */
.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
}

.dark body {
  background: var(--background);
  color: var(--foreground);
}

/* Prose styles for markdown preview */
.prose {
  color: inherit;
  max-width: none;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: inherit;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.prose h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.prose h3 {
  font-size: 1.5rem;
  line-height: 2rem;
}

.prose h4 {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.prose h5 {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.prose h6 {
  font-size: 1rem;
  line-height: 1.5rem;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.75;
}

.prose a {
  color: #3b82f6;
  text-decoration: none;
}

.prose a:hover {
  text-decoration: underline;
}

.prose strong {
  font-weight: 600;
}

.prose em {
  font-style: italic;
}

.prose code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: var(--font-jetbrains-mono);
}

.dark .prose code {
  background-color: #374151;
}

.prose pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.dark .prose pre {
  background-color: #1f2937;
}

.prose pre code {
  background: none;
  padding: 0;
}

.prose blockquote {
  border-left: 4px solid #e5e7eb;
  margin: 1rem 0;
  padding-left: 1rem;
  color: #6b7280;
  font-style: italic;
}

.dark .prose blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

.prose ul,
.prose ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.prose th,
.prose td {
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
  text-align: left;
}

.dark .prose th,
.dark .prose td {
  border-color: #4b5563;
}

.prose th {
  background-color: #f9fafb;
  font-weight: 600;
}

.dark .prose th {
  background-color: #374151;
}

.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
}

.prose hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 2rem 0;
}

.dark .prose hr {
  border-top-color: #4b5563;
}

/* Dark mode prose styles */
.dark .prose {
  color: #f3f4f6;
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3,
.dark .prose h4,
.dark .prose h5,
.dark .prose h6 {
  color: #f9fafb;
}

.dark .prose a {
  color: #60a5fa;
}

.dark .prose strong {
  color: #f9fafb;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-full-width {
    width: 100% !important;
    max-width: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* Animation delay utilities */
.animation-delay-150 {
  animation-delay: 150ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

/* Custom utility classes */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-slow {
  animation: bounce 2s infinite;
}

/* Smooth transitions */
.transition-all-300 {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform-300 {
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-opacity-200 {
  transition: opacity 200ms ease-in-out;
}

/* Hover effects */
.hover-scale {
  transition: transform 200ms ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-lift {
  transition: transform 200ms ease-in-out, box-shadow 200ms ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.focus-ring-dark {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-gray-800;
}

/* Scrollbar utilities */
.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 2px;
}

.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 2px;
}

.dark .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Ensure proper touch targets */
  button, .button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on mobile */
  .prose {
    font-size: 16px;
    line-height: 1.6;
  }

  .prose h1 { font-size: 1.75rem; }
  .prose h2 { font-size: 1.5rem; }
  .prose h3 { font-size: 1.25rem; }
  .prose h4 { font-size: 1.125rem; }
  .prose h5 { font-size: 1rem; }
  .prose h6 { font-size: 0.875rem; }

  /* Better spacing for mobile */
  .prose p {
    margin-bottom: 1.25rem;
  }

  .prose ul, .prose ol {
    padding-left: 1.5rem;
  }

  /* Improve code blocks on mobile */
  .prose pre {
    font-size: 14px;
    padding: 0.75rem;
    margin: 1rem -1rem;
    border-radius: 0;
  }

  .prose code {
    font-size: 14px;
  }

  /* Better table handling on mobile */
  .prose table {
    font-size: 14px;
  }

  .prose th,
  .prose td {
    padding: 0.5rem 0.25rem;
    min-width: 100px;
  }

  /* Improve form elements */
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better touch scrolling */
  .overflow-auto,
  .overflow-y-auto,
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .prose {
    font-size: 15px;
  }

  /* Adjust button sizes for tablet */
  button, .button {
    min-height: 40px;
    min-width: 40px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Ensure crisp text rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
