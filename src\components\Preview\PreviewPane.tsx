/**
 * Preview pane component for rendered markdown
 */

'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useApp } from '@/contexts/AppContext';
import { extractHeadings } from '@/utils/markdown';

interface PreviewPaneProps {
  onScroll?: (scrollTop: number, scrollHeight: number, clientHeight: number) => void;
  scrollToPosition?: { scrollTop: number; scrollHeight: number; clientHeight: number } | null;
}

export function PreviewPane({ onScroll, scrollToPosition }: PreviewPaneProps = {}) {
  const { state, dispatch } = useApp();
  const previewRef = useRef<HTMLDivElement>(null);
  const [showToc, setShowToc] = useState(false);
  const [headings, setHeadings] = useState<Array<{ level: number; text: string; id: string }>>([]);
  const [isScrollSyncing, setIsScrollSyncing] = useState(false);

  // Extract headings for table of contents
  useEffect(() => {
    if (state.editor.content) {
      const extractedHeadings = extractHeadings(state.editor.content);
      setHeadings(extractedHeadings);
    } else {
      setHeadings([]);
    }
  }, [state.editor.content]);

  // Handle scroll synchronization with debouncing
  const handleScroll = useCallback(() => {
    if (!previewRef.current || isScrollSyncing) return;

    if (onScroll) {
      const { scrollTop, scrollHeight, clientHeight } = previewRef.current;
      onScroll(scrollTop, scrollHeight, clientHeight);
    }
  }, [onScroll, isScrollSyncing]);

  // Enhanced scroll synchronization
  useEffect(() => {
    if (!scrollToPosition || !previewRef.current || isScrollSyncing) return;

    const syncScroll = () => {
      setIsScrollSyncing(true);

      const { scrollTop, scrollHeight, clientHeight } = scrollToPosition;
      const previewElement = previewRef.current!;

      // Calculate relative position (0-1)
      const editorProgress = scrollHeight <= clientHeight ? 0 :
        scrollTop / (scrollHeight - clientHeight);

      // Apply to preview with bounds checking
      const previewScrollHeight = previewElement.scrollHeight;
      const previewClientHeight = previewElement.clientHeight;
      const maxScroll = Math.max(0, previewScrollHeight - previewClientHeight);
      
      const targetScrollTop = Math.round(editorProgress * maxScroll);

      // Smooth scroll only if the change is significant
      const currentScroll = previewElement.scrollTop;
      const scrollDiff = Math.abs(currentScroll - targetScrollTop);

      if (scrollDiff > 10) {
        previewElement.scrollTo({
          top: targetScrollTop,
          behavior: scrollDiff > 100 ? 'smooth' : 'auto'
        });
      }

      // Reset sync flag after scroll completes
      const resetSync = () => {
        requestAnimationFrame(() => {
          setIsScrollSyncing(false);
        });
      };

      if (scrollDiff > 100) {
        setTimeout(resetSync, 300);
      } else {
        resetSync();
      }
    };

    requestAnimationFrame(syncScroll);
  }, [scrollToPosition, isScrollSyncing]);

  // Handle anchor link clicks
  const handleAnchorClick = (event: React.MouseEvent) => {
    const target = event.target as HTMLElement;
    if (target.tagName === 'A' && target.getAttribute('href')?.startsWith('#')) {
      event.preventDefault();
      const id = target.getAttribute('href')?.substring(1);
      if (id && previewRef.current) {
        const element = previewRef.current.querySelector(`#${id}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }
    }
  };

  // Handle copy code button clicks
  useEffect(() => {
    const handleCopyCode = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target.classList.contains('copy-code-btn')) {
        const code = decodeURIComponent(target.getAttribute('data-code') || '');
        navigator.clipboard.writeText(code).then(() => {
          target.textContent = 'Copied!';
          setTimeout(() => {
            target.textContent = 'Copy';
          }, 2000);
        }).catch(() => {
          target.textContent = 'Failed';
          setTimeout(() => {
            target.textContent = 'Copy';
          }, 2000);
        });
      }
    };

    const previewElement = previewRef.current;
    if (previewElement) {
      previewElement.addEventListener('click', handleCopyCode);
      return () => previewElement.removeEventListener('click', handleCopyCode);
    }
  }, [state.preview.htmlContent]);

  const scrollToHeading = (id: string) => {
    if (previewRef.current) {
      const element = previewRef.current.querySelector(`#${id}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  // Handle copy as text functionality
  const handleCopyAsText = async () => {
    try {
      // Convert markdown to plain text by removing markdown syntax
      const plainText = state.editor.content
        .replace(/^#{1,6}\s+/gm, '') // Remove headers
        .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
        .replace(/\*(.*?)\*/g, '$1') // Remove italic
        .replace(/`(.*?)`/g, '$1') // Remove inline code
        .replace(/```[\s\S]*?```/g, '') // Remove code blocks
        .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1') // Remove images, keep alt text
        .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
        .replace(/^\s*[-*+]\s+/gm, '• ') // Convert bullet points
        .replace(/^\s*\d+\.\s+/gm, '') // Remove ordered list numbers
        .replace(/^\s*>\s+/gm, '') // Remove blockquotes
        .replace(/\n{3,}/g, '\n\n') // Normalize line breaks
        .trim();

      await navigator.clipboard.writeText(plainText);

      // Show temporary feedback
      const button = document.querySelector('[title="Copy as plain text"]');
      if (button) {
        const originalTitle = button.getAttribute('title');
        button.setAttribute('title', 'Copied!');
        setTimeout(() => {
          button.setAttribute('title', originalTitle || 'Copy as plain text');
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to copy text:', error);
      alert('Failed to copy text to clipboard');
    }
  };

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-900">
      {/* Preview header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Preview</h2>

        <div className="flex items-center space-x-2">
          {/* Copy as text button */}
          {state.editor.content && (
            <button
              onClick={handleCopyAsText}
              className="p-2 rounded-md transition-colors hover:bg-gray-100 dark:hover:bg-gray-700"
              title="Copy as plain text"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </button>
          )}

          {/* Table of contents toggle */}
          {headings.length > 0 && (
            <button
              onClick={() => setShowToc(!showToc)}
              className={`p-2 rounded-md transition-colors ${
                showToc
                  ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-600'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
              title="Table of Contents"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Table of contents sidebar */}
        {showToc && headings.length > 0 && (
          <div className="w-64 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 overflow-y-auto">
            <div className="p-4">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                Table of Contents
              </h3>
              <nav className="space-y-1">
                {headings.map((heading, index) => (
                  <button
                    key={index}
                    onClick={() => scrollToHeading(heading.id)}
                    className={`block w-full text-left text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors ${
                      heading.level > 1 ? `ml-${(heading.level - 1) * 3}` : ''
                    }`}
                    style={{ paddingLeft: `${(heading.level - 1) * 12}px` }}
                  >
                    {heading.text}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        )}

        {/* Preview content */}
        <div className="flex-1 relative">
          {state.editor.currentFile ? (
            <div
              ref={previewRef}
              className="h-full overflow-y-auto overflow-x-hidden p-4 md:p-6 print-full-width preview-content"
              onScroll={handleScroll}
              onClick={handleAnchorClick}
            >
              {state.preview.isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-gray-600 dark:text-gray-400">Rendering preview...</p>
                  </div>
                </div>
              ) : state.preview.error ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-center">
                    <svg className="w-12 h-12 text-red-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-red-600 dark:text-red-400">{state.preview.error}</p>
                  </div>
                </div>
              ) : state.preview.htmlContent ? (
                <div
                  className="prose prose-sm md:prose-base lg:prose-lg dark:prose-invert max-w-none break-words preview-content"
                  dangerouslySetInnerHTML={{ __html: state.preview.htmlContent }}
                />
              ) : (
                <div className="flex items-center justify-center h-32 text-gray-500 dark:text-gray-400">
                  <div className="text-center">
                    <svg className="w-12 h-12 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <p>Start typing to see the preview</p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <h3 className="text-lg font-medium mb-2">No file selected</h3>
                <p className="text-sm">Select a file to see the preview</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Custom styles for preview */}
      <style jsx global>{`
        /* Preview content styles */
        .preview-content {
          -webkit-overflow-scrolling: touch;
          scroll-behavior: smooth;
        }

        /* Print settings */
        @page {
          margin: 2cm;
          size: A4;
        }

        @media print {
          /* Basic layout */
          html, body {
            width: 210mm;
            height: 297mm;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }

          .preview-content {
            height: auto !important;
            overflow: visible !important;
            padding: 0 !important;
          }

          /* Typography */
          .prose {
            max-width: none !important;
            font-family: "Times New Roman", Times, serif !important;
            font-size: 12pt !important;
            line-height: 1.5 !important;
            color: #000 !important;
          }

          .prose * { color: inherit !important; }

          .prose h1 { font-size: 24pt !important; margin: 24pt 0 12pt !important; page-break-after: avoid !important; }
          .prose h2 { font-size: 18pt !important; margin: 18pt 0 9pt !important; page-break-after: avoid !important; }
          .prose h3 { font-size: 14pt !important; margin: 14pt 0 7pt !important; page-break-after: avoid !important; }
          .prose h4 { font-size: 12pt !important; margin: 12pt 0 6pt !important; page-break-after: avoid !important; }
          .prose p { margin: 0 0 12pt !important; orphans: 3 !important; widows: 3 !important; }
          .prose li { font-size: 12pt !important; margin: 0 0 6pt !important; }

          /* Code blocks */
          .code-block-wrapper {
            page-break-inside: avoid !important;
            margin: 16pt 0 !important;
            padding: 12pt !important;
            border: 1pt solid #ccc !important;
            border-radius: 4pt !important;
            background-color: #f8f9fa !important;
          }

          .code-block-wrapper pre,
          .code-block-wrapper code {
            font-family: "Courier New", Courier, monospace !important;
            font-size: 10pt !important;
            line-height: 1.4 !important;
            white-space: pre-wrap !important;
            word-wrap: break-word !important;
          }

          /* Images */
          img {
            max-width: 100% !important;
            max-height: 200mm !important; /* ~70% of A4 height */
            object-fit: contain !important;
            page-break-inside: avoid !important;
            margin: 16pt auto !important;
          }

          /* Tables */
          table {
            width: 100% !important;
            border-collapse: collapse !important;
            page-break-inside: avoid !important;
            margin: 16pt 0 !important;
          }

          th, td {
            border: 0.5pt solid #000 !important;
            padding: 8pt !important;
          }

          /* Links */
          a {
            text-decoration: none !important;
            color: #000 !important;
          }

          a[href^="http"]:after {
            content: " (" attr(href) ")" !important;
            font-size: 10pt !important;
            color: #666 !important;
          }

          /* Hide UI elements */
          .preview-header,
          .code-block-header,
          .copy-code-btn {
            display: none !important;
          }

          /* Force page breaks */
          .page-break-before { page-break-before: always !important; }
          .page-break-after { page-break-after: always !important; }
          h1 { page-break-before: always !important; }
          h1:first-child { page-break-before: avoid !important; }
        }

        /* Typography and layout */
        .prose {
          color: inherit;
          width: 100%;
          max-width: 100%;
          overflow-wrap: break-word;
          word-wrap: break-word;
          word-break: break-word;
          line-height: 1.6;
          font-size: 1rem;
        }

        .prose > * {
          margin-top: 1.25em;
          margin-bottom: 1.25em;
        }

        /* Headings */
        .prose h1 { font-size: 2.25em; margin-top: 2em; margin-bottom: 1em; }
        .prose h2 { font-size: 1.75em; margin-top: 1.75em; margin-bottom: 0.75em; }
        .prose h3 { font-size: 1.5em; margin-top: 1.5em; margin-bottom: 0.5em; }
        .prose h4 { font-size: 1.25em; margin-top: 1.25em; margin-bottom: 0.5em; }

        /* Lists */
        .prose ul, .prose ol {
          padding-left: 1.5em;
          margin: 1em 0;
        }

        .prose li {
          margin: 0.5em 0;
          padding-left: 0.5em;
        }

        .prose li::marker {
          color: #6b7280;
        }

        /* Blockquotes */
        .prose blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 1em;
          margin: 1.5em 0;
          font-style: italic;
          color: #4b5563;
        }

        .dark .prose blockquote {
          border-color: #4b5563;
          color: #9ca3af;
        }

        /* Code blocks and syntax highlighting */
        .prose .code-block-wrapper {
          position: relative;
          margin: 1.5em 0;
          font-size: 0.9em;
          background: #f8f9fa;
          border: 1px solid #e5e7eb;
          border-radius: 0.5em;
          overflow: hidden;
        }

        .dark .prose .code-block-wrapper {
          background: #1f2937;
          border-color: #374151;
        }

        .prose .code-block-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75em 1em;
          background: #f3f4f6;
          border-bottom: 1px solid #e5e7eb;
          font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
        }

        .dark .prose .code-block-header {
          background: #374151;
          border-color: #4b5563;
        }

        .prose pre {
          margin: 0;
          padding: 1em;
          overflow-x: auto;
          font-size: 0.9em;
          line-height: 1.5;
          tab-size: 2;
          -webkit-overflow-scrolling: touch;
          background: transparent;
        }

        .prose code {
          font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
          font-size: 0.9em;
          background: transparent;
          padding: 0;
        }

        .prose .code-language {
          font-size: 0.8em;
          font-weight: 500;
          color: #6b7280;
        }

        .dark .prose .code-language {
          color: #9ca3af;
        }

        .prose .copy-code-btn {
          font-size: 0.8em;
          padding: 0.25em 0.75em;
          border: 1px solid #e5e7eb;
          border-radius: 0.25em;
          background: white;
          color: #374151;
          cursor: pointer;
          transition: all 0.2s;
        }

        .dark .prose .copy-code-btn {
          border-color: #4b5563;
          background: #374151;
          color: #e5e7eb;
        }

        .prose .copy-code-btn:hover {
          background: #f3f4f6;
          border-color: #d1d5db;
        }

        .dark .prose .copy-code-btn:hover {
          background: #4b5563;
          border-color: #6b7280;
        }

        /* Table styles */
        .prose table {
          width: 100%;
          margin: 2em 0;
          border-collapse: separate;
          border-spacing: 0;
          font-size: 0.9em;
        }

        .prose thead {
          background: #f8f9fa;
        }

        .dark .prose thead {
          background: #1f2937;
        }

        .prose th {
          font-weight: 600;
          text-align: left;
        }

        .prose th,
        .prose td {
          padding: 0.75em 1em;
          border: 1px solid #e5e7eb;
        }

        .dark .prose th,
        .dark .prose td {
          border-color: #4b5563;
        }

        .prose tr:nth-child(even) {
          background: #f9fafb;
        }

        .dark .prose tr:nth-child(even) {
          background: #374151;
        }

        .prose .table-wrapper {
          margin: 1.5em 0;
          overflow-x: auto;
          border-radius: 0.5em;
          border: 1px solid #e5e7eb;
        }

        .dark .prose .table-wrapper {
          border-color: #4b5563;
        }

        @media screen and (max-width: 768px) {
          .prose .table-wrapper {
            margin-left: -1rem;
            margin-right: -1rem;
            border-radius: 0;
            border-left: none;
            border-right: none;
          }

          .prose th,
          .prose td {
            padding: 0.5em 0.75em;
            min-width: 120px;
          }
        }
          background: #4b5563;
          border-color: #6b7280;
        }

        /* Mobile optimizations */
        @media screen and (max-width: 768px) {
          .prose {
            font-size: 0.875rem;
            line-height: 1.5;
          }

          .prose pre {
            padding: 0.75rem;
          }

          .prose .code-block-wrapper {
            border-radius: 0;
            margin: 1rem -1rem;
          }
        }

        /* Code block styling */
        .prose .code-block-wrapper {
          position: relative;
          margin: 1.5rem 0;
          max-width: 100%;
          overflow-x: auto;
        }

        .prose pre {
          max-width: 100%;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
        }

        .prose code {
          word-break: break-word;
          white-space: pre-wrap;
        }

        .prose .code-block-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: #f3f4f6;
          border: 1px solid #e5e7eb;
          border-bottom: none;
          border-radius: 0.375rem 0.375rem 0 0;
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }

        .dark .prose .code-block-header {
          background: #374151;
          border-color: #4b5563;
        }

        .prose .code-language {
          font-weight: 500;
          color: #6b7280;
        }

        .dark .prose .code-language {
          color: #9ca3af;
        }

        .prose .copy-code-btn {
          background: #3b82f6;
          color: white;
          border: none;
          border-radius: 0.25rem;
          padding: 0.25rem 0.5rem;
          font-size: 0.75rem;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .prose .copy-code-btn:hover {
          background: #2563eb;
        }

        .prose .code-block-wrapper pre {
          margin: 0;
          border-radius: 0 0 0.375rem 0.375rem;
          border: 1px solid #e5e7eb;
          border-top: none;
        }

        .dark .prose .code-block-wrapper pre {
          border-color: #4b5563;
        }

        .prose .table-wrapper {
          overflow-x: auto;
          margin: 1rem 0;
        }

        .prose .markdown-table {
          width: 100%;
          border-collapse: collapse;
        }

        .prose .markdown-table th,
        .prose .markdown-table td {
          border: 1px solid #e5e7eb;
          padding: 0.5rem;
          text-align: left;
        }

        .dark .prose .markdown-table th,
        .dark .prose .markdown-table td {
          border-color: #4b5563;
        }

        .prose .markdown-table th {
          background: #f9fafb;
          font-weight: 600;
        }

        .dark .prose .markdown-table th {
          background: #374151;
        }

        /* Print styles */
        @media print {
          .prose {
            max-width: none !important;
          }

          .prose .anchor-link {
            display: none;
          }

          .prose .copy-code-btn {
            display: none;
          }

          .prose .code-block-header {
            background: #f9fafb !important;
            color: #000 !important;
          }
        }
      `}</style>
    </div>
  );
}
