/**
 * Markdown processing utilities
 */

'use client';

import { marked } from 'marked';
import DOMPurify from 'dompurify';

// Configure marked with basic options
const options = {
  gfm: true,
  breaks: true,
  pedantic: false,
  mangle: false
};

marked.setOptions(options);

// Process markdown with enhanced features including emojis, marked text, and more
function renderMarkdown(markdown: string): string {
  try {
    // Pre-process markdown for custom features
    let processedMarkdown = String(markdown || '')
      // Handle marked text (==text==)
      .replace(/==(.*?)==/g, '<mark>$1</mark>')

      // Handle inserted text (++text++)
      .replace(/\+\+(.*?)\+\+/g, '<ins>$1</ins>')

      // Handle subscript and superscript
      .replace(/~([^~\s]+)~/g, '<sub>$1</sub>')
      .replace(/\^([^\^\s]+)\^/g, '<sup>$1</sup>')

      // Handle emojis (convert :emoji: to unicode)
      .replace(/:wink:/g, '😉')
      .replace(/:cry:/g, '😢')
      .replace(/:laughing:/g, '😆')
      .replace(/:yum:/g, '😋')
      .replace(/:smile:/g, '😊')
      .replace(/:heart:/g, '❤️')
      .replace(/:thumbsup:/g, '👍')
      .replace(/:thumbsdown:/g, '👎')
      .replace(/:fire:/g, '🔥')
      .replace(/:rocket:/g, '🚀')
      .replace(/:star:/g, '⭐')
      .replace(/:warning:/g, '⚠️')
      .replace(/:info:/g, 'ℹ️')
      .replace(/:check:/g, '✅')
      .replace(/:x:/g, '❌')

      // Handle emoticons
      .replace(/:-\)/g, '🙂')
      .replace(/:-\(/g, '😞')
      .replace(/8-\)/g, '😎')
      .replace(/;\)/g, '😉')

      // Handle typographic replacements
      .replace(/\(c\)/gi, '©')
      .replace(/\(r\)/gi, '®')
      .replace(/\(tm\)/gi, '™')
      .replace(/\(p\)/gi, '℗')
      .replace(/\+-/g, '±')
      .replace(/\.{3}/g, '…')
      .replace(/!{4,}/g, '!!!!')
      .replace(/\?{4,}/g, '????')
      .replace(/,{2,}/g, ',')
      .replace(/--(?!-)/g, '–')
      .replace(/---/g, '—')

      // Handle smart quotes
      .replace(/"([^"]+)"/g, '"$1"')
      .replace(/'([^']+)'/g, ''$1'');

    // Parse markdown to HTML
    const rawHtml = marked.parse(processedMarkdown);

    // Post-process HTML to enhance features
    const enhancedHtml = String(rawHtml)
      // Enhance headings with IDs for navigation
      .replace(/<h([1-6])>(.*?)<\/h[1-6]>/g, (_match, level, content) => {
        const id = content.toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/\s+/g, '-')
          .trim();
        return `<h${level} id="${id}">${content}</h${level}>`;
      })

      // Enhance code blocks
      .replace(
        /<pre><code(?:\s+class="language-([^"]*)")?>([\s\S]*?)<\/code><\/pre>/g,
        (_, lang, code) => {
          const language = String(lang || 'text');
          const decodedCode = String(code)
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'");

          return `
            <div class="code-block-wrapper">
              <div class="code-block-header">
                <span class="code-language">${language}</span>
                <button class="copy-code-btn" data-code="${encodeURIComponent(decodedCode)}">Copy</button>
              </div>
              <pre class="language-${language}"><code class="hljs ${language ? `language-${language}` : ''}">${code}</code></pre>
            </div>
          `;
        }
      )

      // Enhance lists with proper styling
      .replace(/<ul>/g, '<ul class="md-list list-disc list-inside space-y-1">')
      .replace(/<ol>/g, '<ol class="md-list list-decimal list-inside space-y-1">')
      .replace(/<li>/g, '<li class="md-list-item">')

      // Fix nested lists
      .replace(/<ul class="md-list list-disc list-inside space-y-1">\s*<li class="md-list-item">\s*<ul/g, '<ul class="md-list list-disc list-inside space-y-1"><li class="md-list-item"><ul class="list-disc list-inside ml-6 space-y-1">')
      .replace(/<ul class="md-list list-disc list-inside space-y-1">\s*<li class="md-list-item">\s*<ol/g, '<ul class="md-list list-disc list-inside space-y-1"><li class="md-list-item"><ol class="list-decimal list-inside ml-6 space-y-1">')
      .replace(/<ol class="md-list list-decimal list-inside space-y-1">\s*<li class="md-list-item">\s*<ul/g, '<ol class="md-list list-decimal list-inside space-y-1"><li class="md-list-item"><ul class="list-disc list-inside ml-6 space-y-1">')
      .replace(/<ol class="md-list list-decimal list-inside space-y-1">\s*<li class="md-list-item">\s*<ol/g, '<ol class="md-list list-decimal list-inside space-y-1"><li class="md-list-item"><ol class="list-decimal list-inside ml-6 space-y-1">')

      // Enhance tables with alignment support
      .replace(/<table>/g, '<div class="table-container"><table class="md-table">')
      .replace(/<\/table>/g, '</table></div>')
      .replace(/<thead>/g, '<thead class="md-table-header">')
      .replace(/<tbody>/g, '<tbody class="md-table-body">')
      .replace(/<th>/g, '<th class="md-table-cell md-table-header-cell">')
      .replace(/<td>/g, '<td class="md-table-cell">')

      // Handle table alignment
      .replace(/<th class="md-table-cell md-table-header-cell" align="right">/g, '<th class="md-table-cell md-table-header-cell text-right">')
      .replace(/<th class="md-table-cell md-table-header-cell" align="center">/g, '<th class="md-table-cell md-table-header-cell text-center">')
      .replace(/<td class="md-table-cell" align="right">/g, '<td class="md-table-cell text-right">')
      .replace(/<td class="md-table-cell" align="center">/g, '<td class="md-table-cell text-center">')

      // Enhance blockquotes
      .replace(/<blockquote>/g, '<blockquote class="md-blockquote">')

      // Handle external links to open in new window
      .replace(
        /<a\s+href="([^"]*)"([^>]*)>([\s\S]*?)<\/a>/g,
        (_match, href, attrs, text) => {
          const isExternal = href.startsWith('http') || href.startsWith('//');
          const target = isExternal ? ' target="_blank" rel="noopener noreferrer"' : '';
          return `<a href="${href}"${target}${attrs}>${text}</a>`;
        }
      )

      // Add wrapper classes for better styling
      .replace(/^<p>/gm, '<p class="md-paragraph">')
      .replace(/<hr>/g, '<hr class="md-hr">')

      // Handle strikethrough
      .replace(/<del>/g, '<del class="md-strikethrough">');

    return enhancedHtml;
  } catch (error) {
    console.error('Error rendering markdown:', error);
    return '<p>Error rendering markdown</p>';
  }
}

// Process the markdown with custom handling
function processMarkdown(markdown: string): string {
  try {
    const markdownStr = String(markdown || '');
    return renderMarkdown(markdownStr);
  } catch (error) {
    console.error('Error in processMarkdown:', error);
    return `<p>Error processing markdown: ${String(error)}</p>`;
  }
}


// Add delete confirmation handler
export function confirmDelete(message: string = 'Are you sure you want to delete this?'): boolean {
  return window.confirm(message);
}

/**
 * Convert markdown to sanitized HTML
 */
export function markdownToHtml(markdown: string): string {
  try {
    // Ensure input is a string and process it
    const markdownStr = String(markdown || '');
    const html = processMarkdown(markdownStr);

    // Sanitize the HTML with strict string handling
    const cleanHtml = DOMPurify.sanitize(String(html), {
        ALLOWED_TAGS: [
          // Headers and text formatting
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
          'p', 'br', 'strong', 'em', 'b', 'i',
          // Code elements
          'code', 'pre',
          // Layout and containers
          'div', 'span',
          // Interactive elements
          'button', 'a',
          // Lists
          'ul', 'ol', 'li',
          // Block elements
          'blockquote',
          // Tables
          'table', 'thead', 'tbody', 'tr', 'th', 'td'
        ],
        ALLOWED_ATTR: [
          // Common attributes
          'class', 'id', 'style',
          // Accessibility
          'aria-label', 'role',
          // Links
          'href', 'title', 'rel', 'target',
          // Code blocks
          'data-code', 'language',
          // Copy button
          'data-clipboard-text'
        ],
        ADD_TAGS: ['pre', 'code'],
        ADD_ATTR: ['class', 'data-code', 'data-clipboard-text'],
        ALLOW_DATA_ATTR: true,
        USE_PROFILES: { html: true },
        RETURN_DOM_FRAGMENT: false,
        RETURN_DOM: false,
        WHOLE_DOCUMENT: false
    });

    // Ensure final output is a string
    return String(cleanHtml);
  } catch (error) {
    console.error('Error converting markdown to HTML:', error);
    return '<p>Error rendering markdown content</p>';
  }
}

/**
 * Extract headings from markdown for table of contents
 */
export function extractHeadings(markdown: string): Array<{
  level: number;
  text: string;
  id: string;
}> {
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  const headings: Array<{ level: number; text: string; id: string }> = [];

  let match;
  while ((match = headingRegex.exec(markdown)) !== null) {
    const level = match[1].length;
    const text = match[2].trim();
    const id = text.toLowerCase().replace(/[^\w]+/g, '-');

    headings.push({ level, text, id });
  }

  return headings;
}

/**
 * Get word count from markdown
 */
export function getWordCount(markdown: string): {
  words: number;
  characters: number;
  charactersNoSpaces: number;
  paragraphs: number;
  readingTime: number; // in minutes
} {
  // Remove markdown syntax for accurate word count
  const plainText = markdown
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '') // Remove images
    .replace(/^\s*[-*+]\s+/gm, '') // Remove list markers
    .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered list markers
    .replace(/^\s*>\s+/gm, '') // Remove blockquotes
    .replace(/^\s*\|.*\|$/gm, '') // Remove tables
    .replace(/^\s*[-=]{3,}$/gm, '') // Remove horizontal rules
    .trim();

  const words = plainText.split(/\s+/).filter(word => word.length > 0).length;
  const characters = plainText.length;
  const charactersNoSpaces = plainText.replace(/\s/g, '').length;
  const paragraphs = plainText.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;

  // Average reading speed: 200 words per minute
  const readingTime = Math.ceil(words / 200);

  return {
    words,
    characters,
    charactersNoSpaces,
    paragraphs,
    readingTime
  };
}

/**
 * Validate markdown syntax
 */
export function validateMarkdown(markdown: string): {
  isValid: boolean;
  errors: Array<{
    line: number;
    message: string;
    type: 'warning' | 'error';
  }>;
} {
  const errors: Array<{ line: number; message: string; type: 'warning' | 'error' }> = [];
  const lines = markdown.split('\n');

  lines.forEach((line, index) => {
    const lineNumber = index + 1;

    // Check for unmatched brackets
    const openBrackets = (line.match(/\[/g) || []).length;
    const closeBrackets = (line.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
      errors.push({
        line: lineNumber,
        message: 'Unmatched square brackets',
        type: 'warning'
      });
    }

    // Check for unmatched parentheses in links
    const openParens = (line.match(/\(/g) || []).length;
    const closeParens = (line.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      errors.push({
        line: lineNumber,
        message: 'Unmatched parentheses',
        type: 'warning'
      });
    }

    // Check for malformed links
    const linkRegex = /\[([^\]]*)\]\(([^)]*)\)/g;
    let linkMatch;
    while ((linkMatch = linkRegex.exec(line)) !== null) {
      const linkText = linkMatch[1];
      const linkUrl = linkMatch[2];

      if (!linkText.trim()) {
        errors.push({
          line: lineNumber,
          message: 'Link has empty text',
          type: 'warning'
        });
      }

      if (!linkUrl.trim()) {
        errors.push({
          line: lineNumber,
          message: 'Link has empty URL',
          type: 'error'
        });
      }
    }

    // Check for malformed images
    const imageRegex = /!\[([^\]]*)\]\(([^)]*)\)/g;
    let imageMatch;
    while ((imageMatch = imageRegex.exec(line)) !== null) {
      const imageUrl = imageMatch[2];

      if (!imageUrl.trim()) {
        errors.push({
          line: lineNumber,
          message: 'Image has empty URL',
          type: 'error'
        });
      }
    }
  });

  return {
    isValid: errors.filter(e => e.type === 'error').length === 0,
    errors
  };
}

/**
 * Format markdown content
 */
export function formatMarkdown(markdown: string): string {
  return markdown
    .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
    .replace(/[ \t]+$/gm, '') // Remove trailing whitespace
    .replace(/^[ \t]+/gm, (match) => match.replace(/\t/g, '  ')) // Convert tabs to spaces
    .trim();
}
