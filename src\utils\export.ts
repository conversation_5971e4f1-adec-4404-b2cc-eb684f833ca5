/**
 * Export utilities for different file formats
 */

import { ExportError } from '@/types';
import { markdownToHtml } from './markdown';
import { downloadFile } from './file';

// Session management for file cleanup
const activeFiles = new Map<string, { timeout: NodeJS.Timeout; url: string }>();

/**
 * Clean up temporary files after user session ends
 */
export function scheduleFileCleanup(fileId: string, url: string, delayMs: number = 300000) { // 5 minutes default
  // Clear existing timeout if any
  if (activeFiles.has(fileId)) {
    clearTimeout(activeFiles.get(fileId)!.timeout);
  }

  // Schedule cleanup
  const timeout = setTimeout(() => {
    try {
      URL.revokeObjectURL(url);
      activeFiles.delete(fileId);
    } catch (error) {
      console.warn('Failed to cleanup file:', error);
    }
  }, delayMs);

  activeFiles.set(fileId, { timeout, url });
}

/**
 * Cancel file cleanup (user is still active)
 */
export function cancelFileCleanup(fileId: string) {
  if (activeFiles.has(fileId)) {
    clearTimeout(activeFiles.get(fileId)!.timeout);
    activeFiles.delete(fileId);
  }
}

/**
 * Load image from URL and convert to base64
 */
async function loadImageAsBase64(url: string): Promise<string | null> {
  try {
    const response = await fetch(url);
    if (!response.ok) return null;

    const blob = await response.blob();
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => resolve(null);
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.warn('Failed to load image:', error);
    return null;
  }
}

interface ExportOptions {
  format: 'md' | 'txt' | 'html' | 'pdf' | 'docx';
  pageSize?: string;
  margins?: { top: number; right: number; bottom: number; left: number };
  includeMetadata?: boolean;
}

/**
 * Export markdown as plain text
 */
export async function exportAsText(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    // Remove filename from content if it appears at the beginning
    let cleanMarkdown = markdown;
    const filenamePattern = new RegExp(`^${filename.replace(/\.[^/.]+$/, '')}\\s*\\n?`, 'i');
    cleanMarkdown = cleanMarkdown.replace(filenamePattern, '');

    // Convert markdown to plain text with better formatting preservation
    const plainText = cleanMarkdown
      // Handle headers with proper formatting
      .replace(/^#{6}\s+(.+)$/gm, '      $1')
      .replace(/^#{5}\s+(.+)$/gm, '     $1')
      .replace(/^#{4}\s+(.+)$/gm, '    $1')
      .replace(/^#{3}\s+(.+)$/gm, '   $1')
      .replace(/^#{2}\s+(.+)$/gm, '  $1')
      .replace(/^#{1}\s+(.+)$/gm, ' $1')

      // Handle emphasis
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markers
      .replace(/\*(.*?)\*/g, '$1') // Remove italic markers
      .replace(/~~(.*?)~~/g, '$1') // Remove strikethrough
      .replace(/==(.*?)==/g, '$1') // Remove marked text
      .replace(/\+\+(.*?)\+\+/g, '$1') // Remove inserted text

      // Handle code
      .replace(/`(.*?)`/g, '$1') // Remove inline code markers
      .replace(/```[\s\S]*?```/g, (match) => {
        // Preserve code blocks but remove fences
        return match.replace(/```[^\n]*\n?/g, '').replace(/```$/g, '');
      })

      // Handle links and images
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '[Image: $1]') // Images
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1 ($2)') // Links with URLs

      // Handle lists
      .replace(/^\s*[-*+]\s+/gm, '• ') // Convert bullet points
      .replace(/^\s*(\d+)\.\s+/gm, '$1. ') // Keep ordered list numbers

      // Handle blockquotes
      .replace(/^\s*>\s+/gm, '> ') // Keep blockquote markers

      // Handle horizontal rules
      .replace(/^[-*_]{3,}$/gm, '---')

      // Handle emojis (keep as text)
      .replace(/:([a-z_]+):/g, ':$1:')

      // Handle typographic replacements
      .replace(/\(c\)/gi, '©')
      .replace(/\(r\)/gi, '®')
      .replace(/\(tm\)/gi, '™')
      .replace(/\(p\)/gi, '℗')
      .replace(/\+-/g, '±')
      .replace(/\.{3}/g, '…')
      .replace(/--/g, '–')
      .replace(/---/g, '—')

      // Handle quotes
      .replace(/"([^"]+)"/g, '"$1"')
      .replace(/'([^']+)'/g, ''$1'')

      // Normalize line breaks
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    const textFilename = filename.replace(/\.[^/.]+$/, '.txt');
    downloadFile(plainText, textFilename, 'text/plain');

    // Schedule cleanup for the generated file
    const fileId = `txt_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    scheduleFileCleanup(fileId, '', 300000); // 5 minutes
  } catch (error) {
    throw new ExportError('Failed to export as text', 'txt', error);
  }
}

/**
 * Export markdown as HTML
 */
export async function exportAsHtml(
  markdown: string,
  filename: string,
  _options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    const htmlContent = markdownToHtml(markdown);

    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${filename}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.75;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            color: #374151;
            background: #ffffff;
        }

        h1, h2, h3, h4, h5, h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
            color: #111827;
        }

        h1 { font-size: 2.25rem; line-height: 2.5rem; }
        h2 { font-size: 1.875rem; line-height: 2.25rem; }
        h3 { font-size: 1.5rem; line-height: 2rem; }
        h4 { font-size: 1.25rem; line-height: 1.75rem; }
        h5 { font-size: 1.125rem; line-height: 1.75rem; }
        h6 { font-size: 1rem; line-height: 1.5rem; }

        p {
            margin-bottom: 1rem;
            line-height: 1.75;
        }

        /* Code styling to match preview */
        .code-block-wrapper {
            margin: 1rem 0;
            border-radius: 0.5rem;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .code-block-header {
            background-color: #f9fafb;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .code-language {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
        }

        .copy-code-btn {
            display: none; /* Hide in exports */
        }

        .code-block-wrapper pre {
            background-color: #f3f4f6;
            padding: 1rem;
            margin: 0;
            overflow-x: auto;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
        }

        .code-block-wrapper code {
            background: none;
            padding: 0;
            color: #374151;
        }

        /* Inline code */
        code:not(.code-block-wrapper code) {
            background-color: #f3f4f6;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            color: #374151;
        }

        /* Table styling to match preview */
        .table-wrapper {
            overflow-x: auto;
            margin: 1rem 0;
        }

        .markdown-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .markdown-table th,
        .markdown-table td {
            border: 1px solid #e5e7eb;
            padding: 0.5rem;
            text-align: left;
        }

        .markdown-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        blockquote {
            border-left: 4px solid #e5e7eb;
            margin: 1rem 0;
            padding-left: 1rem;
            color: #6b7280;
            font-style: italic;
        }

        img {
            max-width: 100%;
            height: auto;
            border-radius: 0.5rem;
        }

        a {
            color: #3b82f6;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        ul, ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        li {
            margin-bottom: 0.5rem;
        }

        hr {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 2rem 0;
        }

        .anchor-link {
            opacity: 0;
            margin-left: 0.5rem;
            text-decoration: none;
            color: #6b7280;
        }

        h1:hover .anchor-link,
        h2:hover .anchor-link,
        h3:hover .anchor-link,
        h4:hover .anchor-link,
        h5:hover .anchor-link,
        h6:hover .anchor-link {
            opacity: 1;
        }

        @media print {
            body {
                margin: 0;
                padding: 1rem;
            }

            .anchor-link {
                display: none;
            }

            .copy-code-btn {
                display: none;
            }
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;

    const htmlFilename = filename.replace(/\.[^/.]+$/, '.html');
    downloadFile(fullHtml, htmlFilename, 'text/html');

    // Schedule cleanup for the generated file
    const fileId = `html_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    scheduleFileCleanup(fileId, '', 300000); // 5 minutes
  } catch (error) {
    throw new ExportError('Failed to export as HTML', 'html', error);
  }
}

/**
 * Export markdown as PDF with enhanced formatting, clickable links, and images
 */
export async function exportAsPdf(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Lazy load jsPDF
    const { jsPDF } = await import('jspdf');

    // Initialize PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: options.pageSize || 'a4'
    });

    // Get page dimensions
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 20;
    const contentWidth = pageWidth - (margin * 2);

    let yPosition = margin;
    const lineHeight = 7;
    const fontSize = 12;

    pdf.setFontSize(fontSize);

    // Remove filename from content if it appears at the beginning
    let cleanMarkdown = markdown;
    const filenamePattern = new RegExp(`^${filename.replace(/\.[^/.]+$/, '')}\\s*\\n?`, 'i');
    cleanMarkdown = cleanMarkdown.replace(filenamePattern, '');

    // Parse markdown into structured content
    const lines = cleanMarkdown.split('\n');
    let inCodeBlock = false;
    let inTable = false;
    let tableRows: string[][] = [];
    const orderedListCounters: number[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Check if we need a new page
      if (yPosition > pageHeight - margin - 20) {
        pdf.addPage();
        yPosition = margin;
      }

      // Handle code blocks
      if (line.startsWith('```')) {
        if (inCodeBlock) {
          // End code block
          inCodeBlock = false;
          yPosition += 5;
          continue;
        } else {
          // Start code block
          inCodeBlock = true;
          const language = line.substring(3).trim();
          if (language) {
            pdf.setFontSize(10);
            pdf.setFont('helvetica', 'italic');
            pdf.setTextColor(100, 100, 100);
            pdf.text(`Code (${language}):`, margin, yPosition);
            yPosition += lineHeight;
            pdf.setTextColor(0, 0, 0);
          }
          continue;
        }
      }

      if (inCodeBlock) {
        // Render code with background
        pdf.setFillColor(245, 245, 245);
        pdf.rect(margin - 2, yPosition - 5, contentWidth + 4, lineHeight, 'F');
        pdf.setFont('courier', 'normal');
        pdf.setFontSize(10);
        pdf.setTextColor(0, 0, 0);
        const codeSplitLines = pdf.splitTextToSize(line, contentWidth - 10);
        for (const codeLine of codeSplitLines) {
          if (yPosition > pageHeight - margin) {
            pdf.addPage();
            yPosition = margin;
          }
          pdf.text(codeLine, margin + 2, yPosition);
          yPosition += lineHeight;
        }
        continue;
      }

      // Handle tables
      if (line.includes('|') && line.trim().length > 0) {
        if (!inTable) {
          inTable = true;
          tableRows = [];
        }

        const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell.length > 0);
        if (cells.length > 0 && !line.includes('---')) {
          tableRows.push(cells);
        }
        continue;
      } else if (inTable) {
        // End of table, render it
        yPosition = renderEnhancedTable(pdf, tableRows, margin, yPosition, contentWidth, pageHeight);
        inTable = false;
        tableRows = [];
      }

      // Handle headers
      if (line.startsWith('#')) {
        const level = line.match(/^#+/)?.[0].length || 1;
        const text = line.substring(level).trim();

        pdf.setFont('helvetica', 'bold');
        const headerSizes = [18, 16, 14, 13, 12, 11];
        pdf.setFontSize(headerSizes[level - 1] || 11);
        pdf.setTextColor(0, 0, 0);

        yPosition += level === 1 ? 8 : 5;
        const splitLines = pdf.splitTextToSize(text, contentWidth);
        for (const splitLine of splitLines) {
          if (yPosition > pageHeight - margin) {
            pdf.addPage();
            yPosition = margin;
          }
          pdf.text(splitLine, margin, yPosition);
          yPosition += lineHeight + 2;
        }
        yPosition += 3;
        continue;
      }

      // Handle unordered lists with proper bullet points
      if (line.match(/^\s*[-*+]\s/)) {
        const indent = (line.match(/^(\s*)/)?.[1]?.length || 0) / 2;
        const content = line.replace(/^\s*[-*+]\s/, '');

        pdf.setFont('helvetica', 'normal');
        pdf.setFontSize(fontSize);
        pdf.setTextColor(0, 0, 0);

        const indentWidth = margin + (indent * 15);
        const bulletX = indentWidth - 8;

        // Draw bullet point
        pdf.circle(bulletX, yPosition - 2, 1, 'F');

        // Process content with links
        yPosition = renderTextWithLinks(pdf, content, indentWidth, yPosition, contentWidth - (indentWidth - margin), pageHeight, margin, lineHeight);
        yPosition += 2;
        continue;
      }

      // Handle ordered lists with proper numbering
      if (line.match(/^\s*\d+\.\s/)) {
        const match = line.match(/^(\s*)(\d+)\.\s(.*)$/);
        if (match) {
          const indent = match[1].length / 2;
          const number = parseInt(match[2]);
          const content = match[3];

          // Manage list counters for different levels
          while (orderedListCounters.length <= indent) {
            orderedListCounters.push(1);
          }
          orderedListCounters[indent] = number;

          pdf.setFont('helvetica', 'normal');
          pdf.setFontSize(fontSize);
          pdf.setTextColor(0, 0, 0);

          const indentWidth = margin + (indent * 15);

          // Draw number
          pdf.text(`${number}.`, indentWidth - 10, yPosition);

          // Process content with links
          yPosition = renderTextWithLinks(pdf, content, indentWidth, yPosition, contentWidth - (indentWidth - margin), pageHeight, margin, lineHeight);
          yPosition += 2;
          continue;
        }
      }

      // Handle images with actual image embedding
      if (line.startsWith('![') && line.includes('](')) {
        const match = line.match(/!\[([^\]]*)\]\(([^)]+)\)/);
        if (match) {
          const altText = match[1] || 'Image';
          const url = match[2];

          pdf.setFont('helvetica', 'italic');
          pdf.setFontSize(fontSize - 1);
          pdf.setTextColor(100, 100, 100);

          // Try to load and embed actual image
          try {
            if (url.startsWith('data:image/') || url.startsWith('http')) {
              const imageData = await loadImageAsBase64(url);
              if (imageData) {
                // Add image to PDF
                const imgWidth = Math.min(contentWidth, 150);
                const imgHeight = 100; // Default height, could be calculated based on aspect ratio

                if (yPosition + imgHeight > pageHeight - margin) {
                  pdf.addPage();
                  yPosition = margin;
                }

                pdf.addImage(imageData, 'JPEG', margin, yPosition, imgWidth, imgHeight);
                yPosition += imgHeight + 10;

                // Add caption
                pdf.setFont('helvetica', 'italic');
                pdf.setFontSize(fontSize - 2);
                pdf.setTextColor(100, 100, 100);
                pdf.text(`Figure: ${altText}`, margin, yPosition);
                yPosition += lineHeight + 5;
              } else {
                // Fallback to text reference with clickable link
                pdf.text(`[Image: ${altText}]`, margin, yPosition);
                yPosition += lineHeight;
                pdf.setTextColor(0, 100, 200);
                pdf.textWithLink(`View: ${url}`, margin, yPosition, { url });
                yPosition += lineHeight + 3;
              }
            } else {
              // Local file reference
              pdf.text(`[Image: ${altText}] (${url})`, margin, yPosition);
              yPosition += lineHeight + 3;
            }
          } catch (error) {
            console.warn('Failed to load image:', error);
            pdf.text(`[Image: ${altText}]`, margin, yPosition);
            yPosition += lineHeight;
            pdf.setTextColor(0, 100, 200);
            pdf.textWithLink(`View: ${url}`, margin, yPosition, { url });
            yPosition += lineHeight + 3;
          }

          pdf.setTextColor(0, 0, 0);
          pdf.setFont('helvetica', 'normal');
          pdf.setFontSize(fontSize);
          continue;
        }
      }

      // Handle regular paragraphs with links
      if (line.trim() !== '') {
        pdf.setFont('helvetica', 'normal');
        pdf.setFontSize(fontSize);
        pdf.setTextColor(0, 0, 0);

        yPosition = renderTextWithLinks(pdf, line, margin, yPosition, contentWidth, pageHeight, margin, lineHeight);
        yPosition += 3;
      } else {
        // Empty line - add spacing
        yPosition += lineHeight / 2;
      }
    }

    // Render any remaining table
    if (inTable && tableRows.length > 0) {
      renderEnhancedTable(pdf, tableRows, margin, yPosition, contentWidth, pageHeight);
    }

    // Save the PDF
    const pdfFilename = filename.replace(/\.[^/.]+$/, '.pdf');
    pdf.save(pdfFilename);

    // Schedule cleanup for the generated file
    const fileId = `pdf_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    scheduleFileCleanup(fileId, '', 300000); // 5 minutes

  } catch (error) {
    console.error('PDF Export Error:', error);
    throw new ExportError('Failed to export as PDF', 'pdf', error);
  }
}

/**
 * Enhanced table rendering function for PDF
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function renderEnhancedTable(pdf: any, tableRows: string[][], margin: number, startY: number, contentWidth: number, pageHeight: number): number {
  if (tableRows.length === 0) return startY;

  const cellPadding = 4;
  const minRowHeight = 10;
  const maxColWidth = contentWidth / tableRows[0].length;

  let currentY = startY + 5;

  tableRows.forEach((row, rowIndex) => {
    const isHeader = rowIndex === 0;

    // Calculate row height based on content
    let maxCellHeight = minRowHeight;
    row.forEach((cell) => {
      const lines = pdf.splitTextToSize(cell, maxColWidth - (cellPadding * 2));
      const cellHeight = Math.max(minRowHeight, lines.length * 6 + (cellPadding * 2));
      maxCellHeight = Math.max(maxCellHeight, cellHeight);
    });

    // Check if we need a new page
    if (currentY + maxCellHeight > pageHeight - margin) {
      pdf.addPage();
      currentY = margin;
    }

    // Draw row background for header
    if (isHeader) {
      pdf.setFillColor(240, 240, 240);
      pdf.rect(margin, currentY, contentWidth, maxCellHeight, 'F');
    }

    // Draw cells
    row.forEach((cell, colIndex) => {
      const x = margin + (colIndex * maxColWidth);

      // Draw cell border
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.rect(x, currentY, maxColWidth, maxCellHeight);

      // Draw cell text
      pdf.setFont('helvetica', isHeader ? 'bold' : 'normal');
      pdf.setFontSize(10);
      pdf.setTextColor(0, 0, 0);

      const cellText = pdf.splitTextToSize(cell, maxColWidth - (cellPadding * 2));
      let textY = currentY + cellPadding + 4;

      cellText.forEach((line: string) => {
        pdf.text(line, x + cellPadding, textY);
        textY += 6;
      });
    });

    currentY += maxCellHeight;
  });

  return currentY + 10;
}

/**
 * Render text with clickable links
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function renderTextWithLinks(pdf: any, text: string, x: number, y: number, maxWidth: number, pageHeight: number, margin: number, lineHeight: number): number {
  let currentY = y;

  // Find links in the text
  const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  const parts: Array<{text: string, isLink: boolean, url?: string}> = [];
  let lastIndex = 0;
  let match;

  while ((match = linkRegex.exec(text)) !== null) {
    // Add text before link
    if (match.index > lastIndex) {
      parts.push({
        text: text.substring(lastIndex, match.index),
        isLink: false
      });
    }

    // Add link
    parts.push({
      text: match[1],
      isLink: true,
      url: match[2]
    });

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    parts.push({
      text: text.substring(lastIndex),
      isLink: false
    });
  }

  // If no links found, render as normal text
  if (parts.length === 0) {
    parts.push({text, isLink: false});
  }

  // Render each part
  let currentX = x;

  for (const part of parts) {
    if (part.text.trim() === '') continue;

    const lines = pdf.splitTextToSize(part.text, maxWidth - (currentX - x));

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (currentY > pageHeight - margin) {
        pdf.addPage();
        currentY = margin;
        currentX = x;
      }

      if (part.isLink && part.url) {
        pdf.setTextColor(0, 100, 200);
        pdf.textWithLink(line, currentX, currentY, { url: part.url });
      } else {
        pdf.setTextColor(0, 0, 0);
        pdf.text(line, currentX, currentY);
      }

      if (i < lines.length - 1) {
        currentY += lineHeight;
        currentX = x;
      } else {
        currentX += pdf.getTextWidth(line) + 2;
      }
    }
  }

  return currentY + lineHeight;
}



/**
 * Export markdown as DOCX with enhanced formatting, links, and image support
 */
export async function exportAsDocx(
  markdown: string,
  filename: string,
  _options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Dynamic imports
    const docx = await import('docx');
    const marked = await import('marked');

    // Extract needed types and functions
    const {
      Document,
      Packer,
      Paragraph,
      TextRun,
      HeadingLevel,
      TableCell,
      TableRow,
      Table,
      AlignmentType,
      ExternalHyperlink
    } = docx;
    const { marked: markdownParser } = marked;

    // Remove filename from content if it appears at the beginning
    let cleanMarkdown = markdown;
    const filenamePattern = new RegExp(`^${filename.replace(/\.[^/.]+$/, '')}\\s*\\n?`, 'i');
    cleanMarkdown = cleanMarkdown.replace(filenamePattern, '');

    const tokens = markdownParser.lexer(cleanMarkdown);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const paragraphs: any[] = [];

    // Enhanced inline text processing with links, bold, italic, and code
    const processInlineText = (text: string) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const runs: any[] = [];

      // Process links first
      const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
      let lastIndex = 0;
      let match;

      while ((match = linkRegex.exec(text)) !== null) {
        // Add text before link
        if (match.index > lastIndex) {
          const beforeText = text.substring(lastIndex, match.index);
          runs.push(...processFormattingInText(beforeText));
        }

        // Add link
        runs.push(new ExternalHyperlink({
          children: [new TextRun({
            text: match[1],
            style: 'Hyperlink',
            color: '0563C1'
          })],
          link: match[2]
        }));

        lastIndex = match.index + match[0].length;
      }

      // Add remaining text
      if (lastIndex < text.length) {
        const remainingText = text.substring(lastIndex);
        runs.push(...processFormattingInText(remainingText));
      }

      // If no links found, process formatting only
      if (runs.length === 0) {
        runs.push(...processFormattingInText(text));
      }

      return runs.length > 0 ? runs : [new TextRun({ text: text })];
    };

    // Process formatting (bold, italic, code) in text
    const processFormattingInText = (text: string) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const runs: any[] = [];
      let processedText = text;

      // Process bold text
      processedText = processedText.replace(/\*\*(.*?)\*\*/g, (_, content) => {
        runs.push(new TextRun({ text: content, bold: true }));
        return '';
      });

      // Process italic text
      processedText = processedText.replace(/\*(.*?)\*/g, (_, content) => {
        runs.push(new TextRun({ text: content, italics: true }));
        return '';
      });

      // Process inline code
      processedText = processedText.replace(/`(.*?)`/g, (_, content) => {
        runs.push(new TextRun({
          text: content,
          font: 'Consolas',
          size: 20,
          shading: { type: 'solid', color: 'F3F4F6' }
        }));
        return '';
      });

      // Add remaining text
      if (processedText.trim()) {
        runs.push(new TextRun({ text: processedText }));
      }

      return runs;
    };

    // Enhanced list processing with proper bullet points and numbering
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const processList = (listToken: any, level: number = 0) => {
      for (const item of listToken.items) {
        const children = processInlineText(item.text);

        if (listToken.ordered) {
          // Ordered list with proper numbering
          paragraphs.push(new Paragraph({
            children,
            numbering: {
              reference: 'default-numbering',
              level: Math.min(level, 8)
            },
            spacing: { after: 120 },
            indent: { left: level * 360 }
          }));
        } else {
          // Unordered list with bullet points
          paragraphs.push(new Paragraph({
            children,
            bullet: { level: Math.min(level, 8) },
            spacing: { after: 120 },
            indent: { left: level * 360 }
          }));
        }

        // Handle nested lists
        if (item.tokens) {
          for (const subToken of item.tokens) {
            if (subToken.type === 'list') {
              processList(subToken, level + 1);
            }
          }
        }
      }
    };

    // Helper to get heading level safely
    const getHeadingLevel = (depth: number) => {
      const levels = {
        1: HeadingLevel.HEADING_1,
        2: HeadingLevel.HEADING_2,
        3: HeadingLevel.HEADING_3,
        4: HeadingLevel.HEADING_4,
        5: HeadingLevel.HEADING_5,
        6: HeadingLevel.HEADING_6
      };
      return levels[Math.min(depth, 6) as keyof typeof levels] || HeadingLevel.HEADING_1;
    };

    // Process all tokens
    for (const token of tokens) {
      switch (token.type) {
        case 'heading':
          const headingChildren = processInlineText(token.text);
          paragraphs.push(new Paragraph({
            children: headingChildren,
            heading: getHeadingLevel(token.depth),
            spacing: { before: 240, after: 120 }
          }));
          break;

        case 'paragraph':
          const paragraphChildren = processInlineText(token.text);
          paragraphs.push(new Paragraph({
            children: paragraphChildren,
            spacing: { after: 120 }
          }));
          break;

        case 'list':
          processList(token, 0);
          break;

        case 'code':
          paragraphs.push(new Paragraph({
            children: [new TextRun({
              text: token.text,
              font: 'Consolas',
              size: 20
            })],
            spacing: { before: 160, after: 160 },
            shading: { type: 'solid', color: 'F3F4F6' },
            border: {
              top: { style: 'single', size: 1, color: 'E5E7EB' },
              bottom: { style: 'single', size: 1, color: 'E5E7EB' },
              left: { style: 'single', size: 1, color: 'E5E7EB' },
              right: { style: 'single', size: 1, color: 'E5E7EB' }
            }
          }));
          break;

        case 'table':
          if (token.header && token.rows) {
            const table = new Table({
              rows: [
                // Header row
                new TableRow({
                  children: token.header.map((headerText: string) => {
                    const children = processInlineText(headerText);
                    return new TableCell({
                      children: [new Paragraph({
                        children,
                        alignment: AlignmentType.LEFT
                      })],
                      shading: { type: 'solid', color: 'F9FAFB' }
                    });
                  })
                }),
                // Data rows
                ...token.rows.map((row: string[]) =>
                  new TableRow({
                    children: row.map((cellText: string) => {
                      const children = processInlineText(cellText);
                      return new TableCell({
                        children: [new Paragraph({ children })]
                      });
                    })
                  })
                )
              ],
              width: { size: 100, type: 'pct' }
            });
            paragraphs.push(table);
          }
          break;

        case 'blockquote':
          const quoteChildren = processInlineText(token.text);
          paragraphs.push(new Paragraph({
            children: quoteChildren,
            spacing: { before: 160, after: 160 },
            indent: { left: 720 },
            shading: { type: 'solid', color: 'F9FAFB' },
            border: {
              left: { style: 'single', size: 4, color: '3B82F6' }
            }
          }));
          break;

        case 'hr':
          paragraphs.push(new Paragraph({
            children: [new TextRun({ text: '―'.repeat(50) })],
            alignment: AlignmentType.CENTER,
            spacing: { before: 240, after: 240 }
          }));
          break;

        case 'image':
          // Enhanced image handling with actual image embedding
          const imageAltText = token.text || 'Image';
          const imageUrl = token.href;

          try {
            if (imageUrl.startsWith('data:image/') || imageUrl.startsWith('http')) {
              const imageData = await loadImageAsBase64(imageUrl);
              if (imageData) {
                // Try to embed actual image (this would require additional docx image handling)
                // For now, provide enhanced text reference with clickable link
                paragraphs.push(new Paragraph({
                  children: [
                    new TextRun({
                      text: `[Image: ${imageAltText}] `,
                      italics: true,
                      color: '6B7280'
                    }),
                    new ExternalHyperlink({
                      children: [new TextRun({
                        text: 'View Image',
                        style: 'Hyperlink',
                        color: '0563C1'
                      })],
                      link: imageUrl
                    }),
                    new TextRun({
                      text: ` (${imageUrl})`,
                      italics: true,
                      color: '9CA3AF',
                      size: 18
                    })
                  ],
                  spacing: { after: 120 }
                }));
              } else {
                // Fallback to text reference
                paragraphs.push(new Paragraph({
                  children: [
                    new TextRun({
                      text: `[Image: ${imageAltText}] `,
                      italics: true,
                      color: '6B7280'
                    }),
                    new ExternalHyperlink({
                      children: [new TextRun({
                        text: 'View Image',
                        style: 'Hyperlink',
                        color: '0563C1'
                      })],
                      link: imageUrl
                    })
                  ],
                  spacing: { after: 120 }
                }));
              }
            } else {
              // Local file reference
              paragraphs.push(new Paragraph({
                children: [new TextRun({
                  text: `[Image: ${imageAltText}] (${imageUrl})`,
                  italics: true,
                  color: '6B7280'
                })],
                spacing: { after: 120 }
              }));
            }
          } catch (error) {
            console.warn('Failed to process image:', error);
            paragraphs.push(new Paragraph({
              children: [
                new TextRun({
                  text: `[Image: ${imageAltText}] `,
                  italics: true,
                  color: '6B7280'
                }),
                new ExternalHyperlink({
                  children: [new TextRun({
                    text: 'View Image',
                    style: 'Hyperlink',
                    color: '0563C1'
                  })],
                  link: imageUrl
                })
              ],
              spacing: { after: 120 }
            }));
          }
          break;
      }
    }

    // Create document with metadata
    const doc = new Document({
      creator: 'Markdown Editor',
      title: filename.replace(/\.[^/.]+$/, ''),
      description: 'Exported from Markdown Editor',
      sections: [{
        properties: {
          page: {
            margin: {
              top: 1440,    // 1 inch
              right: 1440,
              bottom: 1440,
              left: 1440
            }
          }
        },
        children: paragraphs
      }]
    });

    // Generate buffer and create download
    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace(/\.[^/.]+$/, '.docx');
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Schedule cleanup for the generated file
    const fileId = `docx_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    scheduleFileCleanup(fileId, url, 300000); // 5 minutes

  } catch (error) {
    console.error('DOCX Export Error:', error);
    throw new ExportError('Failed to export as DOCX', 'docx', error);
  }
}

/**
 * Main export function that delegates to specific format handlers
 */
export async function exportAsMarkdown(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    const mdFilename = filename.replace(/\.[^/.]+$/, '.md');
    downloadFile(markdown, mdFilename, 'text/markdown');

    // Schedule cleanup for the generated file
    const fileId = `md_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    scheduleFileCleanup(fileId, '', 300000); // 5 minutes
  } catch (error) {
    throw new ExportError('Failed to export as Markdown', 'md', error);
  }
}

export async function exportMarkdown(
  markdown: string,
  filename: string,
  options: ExportOptions
): Promise<void> {
  switch (options.format) {
    case 'md':
      return exportAsMarkdown(markdown, filename);
    case 'txt':
      return exportAsText(markdown, filename);
    case 'html':
      return exportAsHtml(markdown, filename, options);
    case 'pdf':
      return exportAsPdf(markdown, filename, options);
    case 'docx':
      return exportAsDocx(markdown, filename, options);
    default:
      throw new ExportError(`Unsupported export format: ${options.format}`, options.format);
  }
}

/**
 * Get available export formats
 */
export function getAvailableFormats(): Array<{
  value: string;
  label: string;
  description: string;
}> {
  return [
    {
      value: 'md',
      label: 'Markdown',
      description: 'Export as Markdown file (.md)'
    },
    {
      value: 'txt',
      label: 'Plain Text',
      description: 'Export as plain text file (.txt)'
    },
    {
      value: 'html',
      label: 'HTML',
      description: 'Export as HTML file (.html)'
    },
    {
      value: 'pdf',
      label: 'PDF',
      description: 'Export as PDF document (.pdf)'
    },
    {
      value: 'docx',
      label: 'Word Document',
      description: 'Export as Microsoft Word document (.docx)'
    }
  ];
}
