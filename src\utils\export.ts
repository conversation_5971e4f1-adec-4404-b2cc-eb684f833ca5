/**
 * Export utilities for different file formats
 */

import { ExportError } from '@/types';
import { markdownToHtml } from './markdown';
import { downloadFile } from './file';

interface ExportOptions {
  format: 'md' | 'txt' | 'html' | 'pdf' | 'docx';
  pageSize?: string;
  margins?: { top: number; right: number; bottom: number; left: number };
  includeMetadata?: boolean;
}

/**
 * Export markdown as plain text
 */
export async function exportAsText(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    const textFilename = filename.replace(/\.[^/.]+$/, '.txt');
    downloadFile(markdown, textFilename, 'text/plain');
  } catch (error) {
    throw new ExportError('Failed to export as text', 'txt', error);
  }
}

/**
 * Export markdown as HTML
 */
export async function exportAsHtml(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    const htmlContent = markdownToHtml(markdown);

    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${filename}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.75;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            color: #374151;
            background: #ffffff;
        }

        h1, h2, h3, h4, h5, h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
            color: #111827;
        }

        h1 { font-size: 2.25rem; line-height: 2.5rem; }
        h2 { font-size: 1.875rem; line-height: 2.25rem; }
        h3 { font-size: 1.5rem; line-height: 2rem; }
        h4 { font-size: 1.25rem; line-height: 1.75rem; }
        h5 { font-size: 1.125rem; line-height: 1.75rem; }
        h6 { font-size: 1rem; line-height: 1.5rem; }

        p {
            margin-bottom: 1rem;
            line-height: 1.75;
        }

        /* Code styling to match preview */
        .code-block-wrapper {
            margin: 1rem 0;
            border-radius: 0.5rem;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .code-block-header {
            background-color: #f9fafb;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .code-language {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
        }

        .copy-code-btn {
            display: none; /* Hide in exports */
        }

        .code-block-wrapper pre {
            background-color: #f3f4f6;
            padding: 1rem;
            margin: 0;
            overflow-x: auto;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
        }

        .code-block-wrapper code {
            background: none;
            padding: 0;
            color: #374151;
        }

        /* Inline code */
        code:not(.code-block-wrapper code) {
            background-color: #f3f4f6;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            color: #374151;
        }

        /* Table styling to match preview */
        .table-wrapper {
            overflow-x: auto;
            margin: 1rem 0;
        }

        .markdown-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .markdown-table th,
        .markdown-table td {
            border: 1px solid #e5e7eb;
            padding: 0.5rem;
            text-align: left;
        }

        .markdown-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        blockquote {
            border-left: 4px solid #e5e7eb;
            margin: 1rem 0;
            padding-left: 1rem;
            color: #6b7280;
            font-style: italic;
        }

        img {
            max-width: 100%;
            height: auto;
            border-radius: 0.5rem;
        }

        a {
            color: #3b82f6;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        ul, ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        li {
            margin-bottom: 0.5rem;
        }

        hr {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 2rem 0;
        }

        .anchor-link {
            opacity: 0;
            margin-left: 0.5rem;
            text-decoration: none;
            color: #6b7280;
        }

        h1:hover .anchor-link,
        h2:hover .anchor-link,
        h3:hover .anchor-link,
        h4:hover .anchor-link,
        h5:hover .anchor-link,
        h6:hover .anchor-link {
            opacity: 1;
        }

        @media print {
            body {
                margin: 0;
                padding: 1rem;
            }

            .anchor-link {
                display: none;
            }

            .copy-code-btn {
                display: none;
            }
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;

    const htmlFilename = filename.replace(/\.[^/.]+$/, '.html');
    downloadFile(fullHtml, htmlFilename, 'text/html');
  } catch (error) {
    throw new ExportError('Failed to export as HTML', 'html', error);
  }
}

/**
 * Export markdown as PDF with better text handling
 */
export async function exportAsPdf(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Lazy load jsPDF
    const { jsPDF } = await import('jspdf');

    // Initialize PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: options.pageSize || 'a4'
    });

    // Get page dimensions
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 20;
    const contentWidth = pageWidth - (margin * 2);

    let yPosition = margin;
    const lineHeight = 7;
    const fontSize = 12;

    pdf.setFontSize(fontSize);

    // Simple markdown to text conversion for PDF
    const lines = markdown.split('\n');

    for (const line of lines) {
      // Check if we need a new page
      if (yPosition > pageHeight - margin) {
        pdf.addPage();
        yPosition = margin;
      }

      let processedLine = line;
      let textStyle = 'normal';

      // Handle headers
      if (line.startsWith('# ')) {
        processedLine = line.substring(2);
        pdf.setFontSize(18);
        pdf.setFont('helvetica', 'bold');
        yPosition += 5;
      } else if (line.startsWith('## ')) {
        processedLine = line.substring(3);
        pdf.setFontSize(16);
        pdf.setFont('helvetica', 'bold');
        yPosition += 4;
      } else if (line.startsWith('### ')) {
        processedLine = line.substring(4);
        pdf.setFontSize(14);
        pdf.setFont('helvetica', 'bold');
        yPosition += 3;
      } else if (line.startsWith('- ') || line.startsWith('* ')) {
        processedLine = '• ' + line.substring(2);
        pdf.setFontSize(fontSize);
        pdf.setFont('helvetica', 'normal');
      } else if (/^\d+\.\s/.test(line)) {
        pdf.setFontSize(fontSize);
        pdf.setFont('helvetica', 'normal');
      } else {
        pdf.setFontSize(fontSize);
        pdf.setFont('helvetica', 'normal');
      }

      // Handle bold and italic (basic)
      processedLine = processedLine
        .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markers
        .replace(/\*(.*?)\*/g, '$1')     // Remove italic markers
        .replace(/`(.*?)`/g, '$1');      // Remove code markers

      // Split long lines
      const splitLines = pdf.splitTextToSize(processedLine, contentWidth);

      for (const splitLine of splitLines) {
        if (yPosition > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }

        pdf.text(splitLine, margin, yPosition);
        yPosition += lineHeight;
      }

      // Add extra space after headers
      if (line.startsWith('#')) {
        yPosition += 3;
      }

      // Reset font for next line
      pdf.setFontSize(fontSize);
      pdf.setFont('helvetica', 'normal');
    }

    // Save the PDF
    const pdfFilename = filename.replace(/\.[^/.]+$/, '.pdf');
    pdf.save(pdfFilename);

  } catch (error) {
    console.error('PDF Export Error:', error);
    throw new ExportError('Failed to export as PDF', 'pdf', error);
  }
}

/**
 * Export markdown as DOCX with enhanced formatting and image support
 */
export async function exportAsDocx(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Dynamic imports
    const docx = await import('docx');
    const marked = await import('marked');

    // Extract needed types and functions
    const {
      Document,
      Packer,
      Paragraph,
      TextRun,
      HeadingLevel,
      TableCell,
      TableRow,
      Table,
      AlignmentType
    } = docx;
    const { marked: markdownParser } = marked;

    const tokens = markdownParser.lexer(markdown);
    const paragraphs: any[] = [];

    // Enhanced inline text processing with better formatting
    const processInlineText = (text: string) => {
      const runs: any[] = [];

      // Handle bold, italic, code, and links
      let processedText = text;

      // Process bold text
      processedText = processedText.replace(/\*\*(.*?)\*\*/g, (_, content) => {
        runs.push(new TextRun({ text: content, bold: true }));
        return '';
      });

      // Process italic text
      processedText = processedText.replace(/\*(.*?)\*/g, (_, content) => {
        runs.push(new TextRun({ text: content, italics: true }));
        return '';
      });

      // Process inline code
      processedText = processedText.replace(/`(.*?)`/g, (_, content) => {
        runs.push(new TextRun({
          text: content,
          font: 'Consolas',
          size: 20,
          shading: { type: 'solid', color: 'F3F4F6' }
        }));
        return '';
      });

      // Add remaining text
      if (processedText.trim()) {
        runs.push(new TextRun({ text: processedText }));
      }

      return runs.length > 0 ? runs : [new TextRun({ text: text })];
    };

    // Enhanced list processing with nesting support
    const processList = (listToken: any, level: number = 0) => {
      for (const item of listToken.items) {
        const children = processInlineText(item.text);
        paragraphs.push(new Paragraph({
          children,
          bullet: { level: Math.min(level, 8) },
          spacing: { after: 80 },
          indent: { left: level * 360 }
        }));

        // Handle nested lists
        if (item.tokens) {
          for (const subToken of item.tokens) {
            if (subToken.type === 'list') {
              processList(subToken, level + 1);
            }
          }
        }
      }
    };

    // Helper to get heading level safely
    const getHeadingLevel = (depth: number) => {
      const levels = {
        1: HeadingLevel.HEADING_1,
        2: HeadingLevel.HEADING_2,
        3: HeadingLevel.HEADING_3,
        4: HeadingLevel.HEADING_4,
        5: HeadingLevel.HEADING_5,
        6: HeadingLevel.HEADING_6
      };
      return levels[Math.min(depth, 6) as keyof typeof levels] || HeadingLevel.HEADING_1;
    };

    // Process all tokens
    for (const token of tokens) {
      switch (token.type) {
        case 'heading':
          const headingChildren = processInlineText(token.text);
          paragraphs.push(new Paragraph({
            children: headingChildren,
            heading: getHeadingLevel(token.depth),
            spacing: { before: 240, after: 120 }
          }));
          break;

        case 'paragraph':
          const paragraphChildren = processInlineText(token.text);
          paragraphs.push(new Paragraph({
            children: paragraphChildren,
            spacing: { after: 120 }
          }));
          break;

        case 'list':
          processList(token, 0);
          break;

        case 'code':
          paragraphs.push(new Paragraph({
            children: [new TextRun({
              text: token.text,
              font: 'Consolas',
              size: 20
            })],
            spacing: { before: 160, after: 160 },
            shading: { type: 'solid', color: 'F3F4F6' },
            border: {
              top: { style: 'single', size: 1, color: 'E5E7EB' },
              bottom: { style: 'single', size: 1, color: 'E5E7EB' },
              left: { style: 'single', size: 1, color: 'E5E7EB' },
              right: { style: 'single', size: 1, color: 'E5E7EB' }
            }
          }));
          break;

        case 'table':
          if (token.header && token.rows) {
            const table = new Table({
              rows: [
                // Header row
                new TableRow({
                  children: token.header.map((headerText: string) => {
                    const children = processInlineText(headerText);
                    return new TableCell({
                      children: [new Paragraph({
                        children,
                        alignment: AlignmentType.LEFT
                      })],
                      shading: { type: 'solid', color: 'F9FAFB' }
                    });
                  })
                }),
                // Data rows
                ...token.rows.map((row: string[]) =>
                  new TableRow({
                    children: row.map((cellText: string) => {
                      const children = processInlineText(cellText);
                      return new TableCell({
                        children: [new Paragraph({ children })]
                      });
                    })
                  })
                )
              ],
              width: { size: 100, type: 'pct' }
            });
            paragraphs.push(table);
          }
          break;

        case 'blockquote':
          const quoteChildren = processInlineText(token.text);
          paragraphs.push(new Paragraph({
            children: quoteChildren,
            spacing: { before: 160, after: 160 },
            indent: { left: 720 },
            shading: { type: 'solid', color: 'F9FAFB' },
            border: {
              left: { style: 'single', size: 4, color: '3B82F6' }
            }
          }));
          break;

        case 'hr':
          paragraphs.push(new Paragraph({
            children: [new TextRun({ text: '―'.repeat(50) })],
            alignment: AlignmentType.CENTER,
            spacing: { before: 240, after: 240 }
          }));
          break;

        case 'image':
          // For now, just add image as text reference
          // TODO: Implement proper image embedding when docx library supports it better
          paragraphs.push(new Paragraph({
            children: [new TextRun({
              text: `[Image: ${token.text || 'Image'}] (${token.href})`,
              italics: true,
              color: '6B7280'
            })],
            spacing: { after: 120 }
          }));
          break;
      }
    }

    // Create document with metadata
    const doc = new Document({
      creator: 'Markdown Editor',
      title: filename.replace(/\.[^/.]+$/, ''),
      description: 'Exported from Markdown Editor',
      sections: [{
        properties: {
          page: {
            margin: {
              top: 1440,    // 1 inch
              right: 1440,
              bottom: 1440,
              left: 1440
            }
          }
        },
        children: paragraphs
      }]
    });

    // Generate buffer and create download
    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace(/\.[^/.]+$/, '.docx');
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => URL.revokeObjectURL(url), 100);

  } catch (error) {
    console.error('DOCX Export Error:', error);
    throw new ExportError('Failed to export as DOCX', 'docx', error);
  }
}

/**
 * Main export function that delegates to specific format handlers
 */
export async function exportAsMarkdown(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    const mdFilename = filename.replace(/\.[^/.]+$/, '.md');
    downloadFile(markdown, mdFilename, 'text/markdown');
  } catch (error) {
    throw new ExportError('Failed to export as Markdown', 'md', error);
  }
}

export async function exportMarkdown(
  markdown: string,
  filename: string,
  options: ExportOptions
): Promise<void> {
  switch (options.format) {
    case 'md':
      return exportAsMarkdown(markdown, filename);
    case 'txt':
      return exportAsText(markdown, filename);
    case 'html':
      return exportAsHtml(markdown, filename, options);
    case 'pdf':
      return exportAsPdf(markdown, filename, options);
    case 'docx':
      return exportAsDocx(markdown, filename, options);
    default:
      throw new ExportError(`Unsupported export format: ${options.format}`, options.format);
  }
}

/**
 * Get available export formats
 */
export function getAvailableFormats(): Array<{
  value: string;
  label: string;
  description: string;
}> {
  return [
    {
      value: 'md',
      label: 'Markdown',
      description: 'Export as Markdown file (.md)'
    },
    {
      value: 'txt',
      label: 'Plain Text',
      description: 'Export as plain text file (.txt)'
    },
    {
      value: 'html',
      label: 'HTML',
      description: 'Export as HTML file (.html)'
    },
    {
      value: 'pdf',
      label: 'PDF',
      description: 'Export as PDF document (.pdf)'
    },
    {
      value: 'docx',
      label: 'Word Document',
      description: 'Export as Microsoft Word document (.docx)'
    }
  ];
}
